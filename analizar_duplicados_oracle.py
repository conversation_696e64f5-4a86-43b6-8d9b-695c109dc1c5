#!/usr/bin/env python3
import oracledb

def main():
    try:
        # Conexión a Oracle
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        cursor = connection.cursor()
        
        print("=== ANÁLISIS DE DUPLICADOS EN ORACLE LOG_USR ===")
        print("Fecha: 2025-06-03")
        print("=" * 70)
        
        # 1. Verificar si hay duplicados por userHistId
        print("\n1. DUPLICADOS POR userHistId:")
        cursor.execute("""
            SELECT userHistId, COUNT(*) as cantidad
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            GROUP BY userHistId
            HAVING COUNT(*) > 1
            ORDER BY COUNT(*) DESC
            FETCH FIRST 10 ROWS ONLY
        """)
        duplicados = cursor.fetchall()
        if duplicados:
            print("   Registros duplicados encontrados:")
            for row in duplicados:
                print(f"     {row[0]}: {row[1]} veces")
        else:
            print("   ✅ No hay duplicados por userHistId")
        
        # 2. Verificar registros con mismo userId pero diferente requestType
        print("\n2. MISMO USERID CON DIFERENTES REQUEST_TYPE:")
        cursor.execute("""
            SELECT userId, requestType, COUNT(*) as cantidad
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            AND userId IS NOT NULL
            GROUP BY userId, requestType
            ORDER BY userId, requestType
            FETCH FIRST 20 ROWS ONLY
        """)
        mismo_user = cursor.fetchall()
        print("   Ejemplos de mismo userId con diferentes tipos:")
        current_user = None
        for row in mismo_user:
            if current_user != row[0]:
                if current_user is not None:
                    print()
                current_user = row[0]
                print(f"   Usuario {row[0]}:")
            print(f"     - {row[1]}: {row[2]} registros")
        
        # 3. Analizar patrones de ActivateUser vs AfiliaUser
        print("\n3. PATRÓN ACTIVATEUSER vs AFILIAUSER:")
        cursor.execute("""
            SELECT userId, requestType, COUNT(*) as cantidad
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            AND requestType IN ('ActivateUser', 'AfiliaUser')
            GROUP BY userId, requestType
            ORDER BY userId, requestType
            FETCH FIRST 10 ROWS ONLY
        """)
        activate_afilia = cursor.fetchall()
        print("   Usuarios con ActivateUser y AfiliaUser:")
        current_user = None
        for row in activate_afilia:
            if current_user != row[0]:
                if current_user is not None:
                    print()
                current_user = row[0]
                print(f"   Usuario {row[0]}:")
            print(f"     - {row[1]}: {row[2]} registros")
        
        # 4. Verificar si ActivateUser y AfiliaUser son el mismo usuario
        print("\n4. VERIFICAR SI ACTIVATEUSER = AFILIAUSER:")
        cursor.execute("""
            WITH ACTIVATE_USERS AS (
                SELECT DISTINCT userId
                FROM USR_DATALAKE.LOG_USR
                WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
                AND requestType = 'ActivateUser'
            ),
            AFILIA_USERS AS (
                SELECT DISTINCT userId
                FROM USR_DATALAKE.LOG_USR
                WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
                AND requestType = 'AfiliaUser'
            )
            SELECT 
                (SELECT COUNT(*) FROM ACTIVATE_USERS) as activate_count,
                (SELECT COUNT(*) FROM AFILIA_USERS) as afilia_count,
                (SELECT COUNT(*) FROM ACTIVATE_USERS A INNER JOIN AFILIA_USERS F ON A.userId = F.userId) as both_count
            FROM DUAL
        """)
        result = cursor.fetchone()
        print(f"   ActivateUser únicos: {result[0]:,}")
        print(f"   AfiliaUser únicos: {result[1]:,}")
        print(f"   Usuarios en ambos: {result[2]:,}")
        
        if result[0] == result[1] == result[2]:
            print("   🚨 CONFIRMADO: ActivateUser y AfiliaUser son DUPLICADOS del mismo usuario")
        else:
            print("   ✅ ActivateUser y AfiliaUser son usuarios diferentes")
        
        # 5. Analizar Delete User vs ClosedUserAccount vs ClosedAccount
        print("\n5. PATRÓN DELETE USER vs CLOSED ACCOUNTS:")
        cursor.execute("""
            SELECT userId, requestType, COUNT(*) as cantidad
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            AND requestType IN ('Delete User', 'ClosedUserAccount', 'ClosedAccount')
            GROUP BY userId, requestType
            ORDER BY userId, requestType
            FETCH FIRST 15 ROWS ONLY
        """)
        delete_closed = cursor.fetchall()
        print("   Usuarios con Delete/Closed:")
        current_user = None
        for row in delete_closed:
            if current_user != row[0]:
                if current_user is not None:
                    print()
                current_user = row[0]
                print(f"   Usuario {row[0]}:")
            print(f"     - {row[1]}: {row[2]} registros")
        
        # 6. Verificar si Delete User genera automáticamente ClosedUserAccount y ClosedAccount
        print("\n6. VERIFICAR PATRÓN DELETE → CLOSED:")
        cursor.execute("""
            WITH DELETE_USERS AS (
                SELECT DISTINCT userId
                FROM USR_DATALAKE.LOG_USR
                WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
                AND requestType = 'Delete User'
            ),
            CLOSED_ACCOUNT_USERS AS (
                SELECT DISTINCT userId
                FROM USR_DATALAKE.LOG_USR
                WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
                AND requestType = 'ClosedUserAccount'
            ),
            CLOSED_USER_USERS AS (
                SELECT DISTINCT userId
                FROM USR_DATALAKE.LOG_USR
                WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
                AND requestType = 'ClosedAccount'
            )
            SELECT 
                (SELECT COUNT(*) FROM DELETE_USERS) as delete_count,
                (SELECT COUNT(*) FROM CLOSED_ACCOUNT_USERS) as closed_account_count,
                (SELECT COUNT(*) FROM CLOSED_USER_USERS) as closed_user_count,
                (SELECT COUNT(*) FROM DELETE_USERS D 
                 INNER JOIN CLOSED_ACCOUNT_USERS CA ON D.userId = CA.userId
                 INNER JOIN CLOSED_USER_USERS CU ON D.userId = CU.userId) as all_three_count
            FROM DUAL
        """)
        result = cursor.fetchone()
        print(f"   Delete User únicos: {result[0]:,}")
        print(f"   ClosedUserAccount únicos: {result[1]:,}")
        print(f"   ClosedAccount únicos: {result[2]:,}")
        print(f"   Usuarios en los 3 tipos: {result[3]:,}")
        
        if result[0] == result[1] == result[2] == result[3]:
            print("   🚨 CONFIRMADO: Delete User genera automáticamente ClosedUserAccount y ClosedAccount")
        else:
            print("   ✅ Delete User, ClosedUserAccount y ClosedAccount son eventos independientes")
        
        # 7. Resumen de transformaciones
        print("\n" + "=" * 70)
        print("RESUMEN DE TRANSFORMACIONES ORACLE:")
        print("=" * 70)
        
        cursor.execute("""
            SELECT requestType, COUNT(*) as cantidad
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            GROUP BY requestType
            ORDER BY COUNT(*) DESC
        """)
        all_types = cursor.fetchall()
        
        total_registros = sum(row[1] for row in all_types)
        print(f"Total registros LOG_USR: {total_registros:,}")
        print("\nDistribución por tipo:")
        for row in all_types:
            porcentaje = (row[1] / total_registros) * 100
            print(f"  {row[0]}: {row[1]:,} ({porcentaje:.1f}%)")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
