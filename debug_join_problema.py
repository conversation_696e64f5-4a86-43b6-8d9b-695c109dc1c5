#!/usr/bin/env python3
"""
Debug del problema de JOIN entre USER_AUTH_CHANGE_HISTORY y USER_DATA_TRX
"""

import pandas as pd
import duckdb

def debug_auth_change_join():
    """Debuggear el JOIN problemático"""
    try:
        conn = duckdb.connect()
        
        user_data_path = "S3_LOG_USER/TEMP_LOGS_USUARIOS/20250603/USER_DATA_TRX.parquet"
        auth_change_path = "S3_LOG_USER/TEMP_LOGS_USUARIOS/20250603/USER_AUTH_CHANGE_HISTORY.parquet"
        
        print("=== DEBUG JOIN PROBLEM ===")
        
        # Verificar datos en USER_AUTH_CHANGE_HISTORY
        query1 = f"""
        SELECT 
            AUTHENTICATION_ID,
            MODIFIED_ON,
            MODIFICATION_TYPE,
            MODIFIED_BY
        FROM read_parquet('{auth_change_path}')
        LIMIT 10
        """
        
        df_auth = conn.execute(query1).df()
        print("Ejemplos USER_AUTH_CHANGE_HISTORY:")
        print(df_auth.to_string(index=False))
        
        # Verificar si AUTHENTICATION_ID existe en USER_DATA_TRX
        query2 = f"""
        SELECT 
            COUNT(*) as TOTAL_AUTH_RECORDS,
            COUNT(DISTINCT AUTHENTICATION_ID) as UNIQUE_AUTH_IDS
        FROM read_parquet('{auth_change_path}')
        """
        
        df_auth_stats = conn.execute(query2).df()
        print(f"\nEstadísticas AUTH_CHANGE:")
        print(df_auth_stats.to_string(index=False))
        
        # Verificar algunos AUTHENTICATION_ID en USER_DATA_TRX
        query3 = f"""
        WITH AUTH_IDS AS (
            SELECT DISTINCT AUTHENTICATION_ID 
            FROM read_parquet('{auth_change_path}')
            LIMIT 10
        )
        SELECT 
            AI.AUTHENTICATION_ID,
            UD.O_USER_ID,
            UD.ID_TYPE,
            UD.ID_VALUE
        FROM AUTH_IDS AI
        LEFT JOIN read_parquet('{user_data_path}') UD ON AI.AUTHENTICATION_ID = UD.O_USER_ID
        """
        
        df_join_test = conn.execute(query3).df()
        print(f"\nTest JOIN AUTH_ID -> USER_DATA:")
        print(df_join_test.to_string(index=False))
        
        # Contar cuántos AUTH_IDs NO tienen match
        query4 = f"""
        SELECT 
            COUNT(*) as TOTAL_AUTH_IDS,
            COUNT(UD.O_USER_ID) as MATCHED_IDS,
            COUNT(*) - COUNT(UD.O_USER_ID) as UNMATCHED_IDS
        FROM (SELECT DISTINCT AUTHENTICATION_ID FROM read_parquet('{auth_change_path}')) AUTH
        LEFT JOIN read_parquet('{user_data_path}') UD ON AUTH.AUTHENTICATION_ID = UD.O_USER_ID
        """
        
        df_match_stats = conn.execute(query4).df()
        print(f"\nEstadísticas de MATCH:")
        print(df_match_stats.to_string(index=False))
        
        return df_match_stats
        
    except Exception as e:
        print(f"Error en debug: {e}")
        return None

def check_oracle_logic():
    """Verificar la lógica original de Oracle para AUTH_CHANGE"""
    try:
        print("\n=== VERIFICAR LÓGICA ORACLE ===")
        
        # Buscar en el código original cómo Oracle maneja AUTH_CHANGE
        with open('sp/SP_LOG_USR.sql', 'r') as f:
            content = f.read()
            
        # Buscar secciones relacionadas con AUTH
        lines = content.split('\n')
        auth_sections = []
        
        for i, line in enumerate(lines):
            if 'AUTH' in line.upper() and ('CHANGE' in line.upper() or 'FACTOR' in line.upper()):
                # Capturar contexto
                start = max(0, i-5)
                end = min(len(lines), i+10)
                auth_sections.append({
                    'line_num': i+1,
                    'context': '\n'.join(lines[start:end])
                })
        
        print("Secciones relacionadas con AUTH en Oracle:")
        for section in auth_sections[:3]:  # Mostrar primeras 3
            print(f"\nLínea {section['line_num']}:")
            print(section['context'])
            print("-" * 40)
            
    except Exception as e:
        print(f"Error verificando lógica Oracle: {e}")

def propose_solution():
    """Proponer solución basada en el análisis"""
    print("\n=== PROPUESTA DE SOLUCIÓN ===")
    print("PROBLEMA IDENTIFICADO:")
    print("- Los AUTHENTICATION_ID en USER_AUTH_CHANGE_HISTORY no coinciden con O_USER_ID en USER_DATA_TRX")
    print("- Esto causa que el LEFT JOIN falle y los campos queden vacíos")
    print()
    print("POSIBLES SOLUCIONES:")
    print("1. Verificar si AUTHENTICATION_ID debe mapearse a otro campo (USER_ID en lugar de O_USER_ID)")
    print("2. Usar un mapeo diferente o tabla intermedia")
    print("3. Aplicar lógica de fallback para casos sin match")
    print("4. Revisar la lógica original de Oracle para AUTH_CHANGE")

def main():
    """Función principal"""
    print("Iniciando debug del problema de JOIN")
    print("="*50)
    
    # Debug del JOIN problemático
    debug_auth_change_join()
    
    # Verificar lógica Oracle
    check_oracle_logic()
    
    # Proponer solución
    propose_solution()

if __name__ == "__main__":
    main()
