#!/usr/bin/env python3
import oracledb
import duckdb
import boto3
import pandas as pd

def main():
    try:
        # Conexión a Oracle
        oracle_conn = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        oracle_cursor = oracle_conn.cursor()
        
        # Configurar DuckDB
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        duck_conn = duckdb.connect()
        duck_conn.sql('INSTALL httpfs;')
        duck_conn.sql('LOAD httpfs;')
        duck_conn.sql('SET s3_region=\'us-east-1\';')
        duck_conn.sql('SET s3_use_ssl=true;')
        duck_conn.sql('SET s3_url_style=\'path\';')
        duck_conn.sql(f'SET s3_access_key_id=\'{credentials.access_key}\';')
        duck_conn.sql(f'SET s3_secret_access_key=\'{credentials.secret_key}\';')
        if credentials.token:
            duck_conn.sql(f'SET s3_session_token=\'{credentials.token}\';')
        
        print("=== VALIDACIÓN DE CONTENIDO DE DATOS - DATA ENGINEERING ===")
        print("Comparación detallada: Oracle LOG_USR vs Pipeline S3")
        print("=" * 80)
        
        # 1. Validación de USERHISTID
        print("\n1. VALIDACIÓN USERHISTID:")
        print("-" * 50)
        
        # Oracle - patrones de USERHISTID
        oracle_cursor.execute("""
            SELECT 
                SUBSTR(USERHISTID, 1, 3) as prefix,
                COUNT(*) as cantidad,
                MIN(LENGTH(USERHISTID)) as min_length,
                MAX(LENGTH(USERHISTID)) as max_length
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            GROUP BY SUBSTR(USERHISTID, 1, 3)
            ORDER BY COUNT(*) DESC
        """)
        oracle_userhistid = oracle_cursor.fetchall()
        
        print("Oracle USERHISTID patterns:")
        for row in oracle_userhistid:
            print(f"   {row[0]}: {row[1]:,} registros (length: {row[2]}-{row[3]})")
        
        # S3 - patrones de USERHISTID
        try:
            result = duck_conn.sql('''
                SELECT 
                    SUBSTR(USERHISTID, 1, 3) as prefix,
                    COUNT(*) as cantidad,
                    MIN(LENGTH(USERHISTID)) as min_length,
                    MAX(LENGTH(USERHISTID)) as max_length
                FROM read_parquet("output/********/LOG_USR.parquet")
                GROUP BY SUBSTR(USERHISTID, 1, 3)
                ORDER BY COUNT(*) DESC
            ''')
            s3_userhistid = result.fetchall()
            
            print("\nS3 USERHISTID patterns:")
            for row in s3_userhistid:
                print(f"   {row[0]}: {row[1]:,} registros (length: {row[2]}-{row[3]})")
                
        except Exception as e:
            print(f"Error S3 USERHISTID: {e}")
        
        # 2. Validación de REQUESTTYPE
        print("\n2. VALIDACIÓN REQUESTTYPE:")
        print("-" * 50)
        
        # Oracle REQUESTTYPE
        oracle_cursor.execute("""
            SELECT REQUESTTYPE, COUNT(*) as cantidad
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            GROUP BY REQUESTTYPE
            ORDER BY COUNT(*) DESC
        """)
        oracle_requesttype = oracle_cursor.fetchall()
        oracle_rt_dict = {row[0]: row[1] for row in oracle_requesttype}
        
        print("Oracle REQUESTTYPE:")
        for row in oracle_requesttype:
            print(f"   {row[0]}: {row[1]:,}")
        
        # S3 REQUESTTYPE
        try:
            result = duck_conn.sql('''
                SELECT REQUESTTYPE, COUNT(*) as cantidad
                FROM read_parquet("output/********/LOG_USR.parquet")
                GROUP BY REQUESTTYPE
                ORDER BY COUNT(*) DESC
            ''')
            s3_requesttype = result.fetchall()
            s3_rt_dict = {row[0]: row[1] for row in s3_requesttype}
            
            print("\nS3 REQUESTTYPE:")
            for row in s3_requesttype:
                print(f"   {row[0]}: {row[1]:,}")
            
            # Comparación REQUESTTYPE
            print("\nCOMPARACIÓN REQUESTTYPE:")
            all_types = set(oracle_rt_dict.keys()) | set(s3_rt_dict.keys())
            for req_type in sorted(all_types):
                oracle_count = oracle_rt_dict.get(req_type, 0)
                s3_count = s3_rt_dict.get(req_type, 0)
                diff = s3_count - oracle_count
                status = "✅" if diff == 0 else "❌" if diff < 0 else "⚠️"
                print(f"   {req_type:<25} Oracle: {oracle_count:>6,} S3: {s3_count:>6,} Diff: {diff:>6,} {status}")
                
        except Exception as e:
            print(f"Error S3 REQUESTTYPE: {e}")
        
        # 3. Validación de BANKDOMAIN
        print("\n3. VALIDACIÓN BANKDOMAIN:")
        print("-" * 50)
        
        # Oracle BANKDOMAIN
        oracle_cursor.execute("""
            SELECT 
                CASE WHEN BANKDOMAIN IS NULL OR BANKDOMAIN = '' THEN '[EMPTY]' ELSE BANKDOMAIN END as domain,
                COUNT(*) as cantidad
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            GROUP BY BANKDOMAIN
            ORDER BY COUNT(*) DESC
        """)
        oracle_bankdomain = oracle_cursor.fetchall()
        oracle_bd_dict = {row[0]: row[1] for row in oracle_bankdomain}
        
        print("Oracle BANKDOMAIN:")
        for row in oracle_bankdomain:
            print(f"   {row[0]}: {row[1]:,}")
        
        # S3 BANKDOMAIN
        try:
            result = duck_conn.sql('''
                SELECT 
                    CASE WHEN BANKDOMAIN IS NULL OR BANKDOMAIN = '' THEN '[EMPTY]' ELSE BANKDOMAIN END as domain,
                    COUNT(*) as cantidad
                FROM read_parquet("output/********/LOG_USR.parquet")
                GROUP BY BANKDOMAIN
                ORDER BY COUNT(*) DESC
            ''')
            s3_bankdomain = result.fetchall()
            s3_bd_dict = {row[0]: row[1] for row in s3_bankdomain}
            
            print("\nS3 BANKDOMAIN:")
            for row in s3_bankdomain:
                print(f"   {row[0]}: {row[1]:,}")
            
            # Comparación BANKDOMAIN
            print("\nCOMPARACIÓN BANKDOMAIN:")
            all_domains = set(oracle_bd_dict.keys()) | set(s3_bd_dict.keys())
            for domain in sorted(all_domains):
                oracle_count = oracle_bd_dict.get(domain, 0)
                s3_count = s3_bd_dict.get(domain, 0)
                diff = s3_count - oracle_count
                status = "✅" if diff == 0 else "❌" if diff < 0 else "⚠️"
                print(f"   {domain:<15} Oracle: {oracle_count:>6,} S3: {s3_count:>6,} Diff: {diff:>6,} {status}")
                
        except Exception as e:
            print(f"Error S3 BANKDOMAIN: {e}")
        
        # 4. Validación de TIPODOCUMENTO
        print("\n4. VALIDACIÓN TIPODOCUMENTO:")
        print("-" * 50)
        
        # Oracle TIPODOCUMENTO
        oracle_cursor.execute("""
            SELECT 
                CASE WHEN TIPODOCUMENTO IS NULL OR TIPODOCUMENTO = '' THEN '[EMPTY]' ELSE TIPODOCUMENTO END as tipo,
                COUNT(*) as cantidad
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            GROUP BY TIPODOCUMENTO
            ORDER BY COUNT(*) DESC
        """)
        oracle_tipodoc = oracle_cursor.fetchall()
        
        print("Oracle TIPODOCUMENTO:")
        for row in oracle_tipodoc:
            print(f"   {row[0]}: {row[1]:,}")
        
        # S3 TIPODOCUMENTO
        try:
            result = duck_conn.sql('''
                SELECT 
                    CASE WHEN TIPODOCUMENTO IS NULL OR TIPODOCUMENTO = '' THEN '[EMPTY]' ELSE TIPODOCUMENTO END as tipo,
                    COUNT(*) as cantidad
                FROM read_parquet("output/********/LOG_USR.parquet")
                GROUP BY TIPODOCUMENTO
                ORDER BY COUNT(*) DESC
            ''')
            s3_tipodoc = result.fetchall()
            
            print("\nS3 TIPODOCUMENTO:")
            for row in s3_tipodoc:
                print(f"   {row[0]}: {row[1]:,}")
                
        except Exception as e:
            print(f"Error S3 TIPODOCUMENTO: {e}")
        
        # 5. Validación de fechas CREATEDON
        print("\n5. VALIDACIÓN CREATEDON:")
        print("-" * 50)
        
        # Oracle fechas
        oracle_cursor.execute("""
            SELECT 
                TO_CHAR(CREATEDON, 'YYYY-MM-DD HH24') as hour_group,
                COUNT(*) as cantidad
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            GROUP BY TO_CHAR(CREATEDON, 'YYYY-MM-DD HH24')
            ORDER BY hour_group
        """)
        oracle_dates = oracle_cursor.fetchall()
        
        print("Oracle CREATEDON por hora:")
        for row in oracle_dates[:10]:  # Primeras 10 horas
            print(f"   {row[0]}: {row[1]:,}")
        if len(oracle_dates) > 10:
            print(f"   ... y {len(oracle_dates) - 10} horas más")
        
        # S3 fechas
        try:
            result = duck_conn.sql('''
                SELECT 
                    STRFTIME(CREATEDON, '%Y-%m-%d %H') as hour_group,
                    COUNT(*) as cantidad
                FROM read_parquet("output/********/LOG_USR.parquet")
                GROUP BY STRFTIME(CREATEDON, '%Y-%m-%d %H')
                ORDER BY hour_group
            ''')
            s3_dates = result.fetchall()
            
            print("\nS3 CREATEDON por hora:")
            for row in s3_dates[:10]:  # Primeras 10 horas
                print(f"   {row[0]}: {row[1]:,}")
            if len(s3_dates) > 10:
                print(f"   ... y {len(s3_dates) - 10} horas más")
                
        except Exception as e:
            print(f"Error S3 CREATEDON: {e}")
        
        # 6. Resumen de validación
        print("\n6. RESUMEN DE VALIDACIÓN DE CONTENIDO:")
        print("=" * 80)
        
        issues = []
        
        # Verificar si hay diferencias significativas
        try:
            total_oracle = sum(oracle_rt_dict.values())
            total_s3 = sum(s3_rt_dict.values())
            
            print(f"Total registros Oracle: {total_oracle:,}")
            print(f"Total registros S3: {total_s3:,}")
            print(f"Diferencia: {total_s3 - total_oracle:,}")
            
            if abs(total_s3 - total_oracle) <= 100:  # Tolerancia de 100 registros
                print("✅ CANTIDAD: ACEPTABLE (diferencia < 100)")
            else:
                print("❌ CANTIDAD: DIFERENCIA SIGNIFICATIVA")
                issues.append("Diferencia significativa en cantidad total")
            
            # Verificar tipos de request
            missing_types = set(oracle_rt_dict.keys()) - set(s3_rt_dict.keys())
            extra_types = set(s3_rt_dict.keys()) - set(oracle_rt_dict.keys())
            
            if not missing_types and not extra_types:
                print("✅ REQUESTTYPE: TIPOS IDÉNTICOS")
            else:
                if missing_types:
                    print(f"❌ REQUESTTYPE: Faltan tipos: {missing_types}")
                    issues.append(f"Tipos faltantes: {missing_types}")
                if extra_types:
                    print(f"⚠️ REQUESTTYPE: Tipos extra: {extra_types}")
            
            # Verificar dominios de banco
            missing_domains = set(oracle_bd_dict.keys()) - set(s3_bd_dict.keys())
            extra_domains = set(s3_bd_dict.keys()) - set(oracle_bd_dict.keys())
            
            if not missing_domains and not extra_domains:
                print("✅ BANKDOMAIN: DOMINIOS IDÉNTICOS")
            else:
                if missing_domains:
                    print(f"❌ BANKDOMAIN: Faltan dominios: {missing_domains}")
                    issues.append(f"Dominios faltantes: {missing_domains}")
                if extra_domains:
                    print(f"⚠️ BANKDOMAIN: Dominios extra: {extra_domains}")
            
        except Exception as e:
            print(f"Error en resumen: {e}")
            issues.append(f"Error en validación: {e}")
        
        # Conclusión final
        print(f"\n7. CONCLUSIÓN FINAL:")
        print("=" * 80)
        
        if not issues:
            print("🎉 VALIDACIÓN DE CONTENIDO: 100% EXITOSA")
            print("✅ Los datos son idénticos entre Oracle y S3")
            print("✅ Estructura y contenido validados")
            print("✅ Pipeline listo para producción")
        else:
            print(f"⚠️ VALIDACIÓN PARCIAL: {len(issues)} problemas encontrados")
            for i, issue in enumerate(issues, 1):
                print(f"   {i}. {issue}")
            print("\n🔧 ACCIONES REQUERIDAS:")
            print("   - Revisar diferencias identificadas")
            print("   - Ajustar pipeline según sea necesario")
        
        oracle_cursor.close()
        oracle_conn.close()
        duck_conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
