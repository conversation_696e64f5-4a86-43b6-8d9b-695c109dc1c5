#!/usr/bin/env python3
import oracledb
import duckdb
import boto3

def main():
    try:
        # Conexión a Oracle
        oracle_conn = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        oracle_cursor = oracle_conn.cursor()
        
        # Configurar DuckDB
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        duck_conn = duckdb.connect()
        duck_conn.sql('INSTALL httpfs;')
        duck_conn.sql('LOAD httpfs;')
        duck_conn.sql('SET s3_region=\'us-east-1\';')
        duck_conn.sql('SET s3_use_ssl=true;')
        duck_conn.sql('SET s3_url_style=\'path\';')
        duck_conn.sql(f'SET s3_access_key_id=\'{credentials.access_key}\';')
        duck_conn.sql(f'SET s3_secret_access_key=\'{credentials.secret_key}\';')
        if credentials.token:
            duck_conn.sql(f'SET s3_session_token=\'{credentials.token}\';')
        
        print("=== VALIDACIÓN ESTRUCTURA DE DATOS - DATA ENGINEERING ===")
        print("Comparación: Oracle LOG_USR vs Pipeline S3")
        print("=" * 80)
        
        # 1. Estructura Oracle LOG_USR
        print("\n1. ESTRUCTURA ORACLE LOG_USR:")
        print("-" * 50)
        oracle_cursor.execute("""
            SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, DATA_PRECISION, DATA_SCALE, NULLABLE
            FROM ALL_TAB_COLUMNS
            WHERE TABLE_NAME = 'LOG_USR'
            AND OWNER = 'USR_DATALAKE'
            ORDER BY COLUMN_ID
        """)
        oracle_columns = oracle_cursor.fetchall()
        
        print(f"{'#':<3} {'COLUMN_NAME':<25} {'DATA_TYPE':<15} {'LENGTH':<8} {'PREC':<5} {'SCALE':<5} {'NULL':<5}")
        print("-" * 80)
        
        oracle_schema = {}
        for i, col in enumerate(oracle_columns, 1):
            col_name, data_type, length, precision, scale, nullable = col
            oracle_schema[col_name] = {
                'type': data_type,
                'length': length,
                'precision': precision,
                'scale': scale,
                'nullable': nullable
            }
            
            length_str = str(length) if length else ''
            precision_str = str(precision) if precision else ''
            scale_str = str(scale) if scale else ''
            nullable_str = 'Y' if nullable == 'Y' else 'N'
            
            print(f"{i:<3} {col_name:<25} {data_type:<15} {length_str:<8} {precision_str:<5} {scale_str:<5} {nullable_str:<5}")
        
        print(f"\nTotal columnas Oracle: {len(oracle_columns)}")
        
        # 2. Estructura nuestro pipeline S3
        print("\n2. ESTRUCTURA PIPELINE S3:")
        print("-" * 50)
        
        try:
            result = duck_conn.sql('DESCRIBE SELECT * FROM read_parquet("S3_LOG_USER/output/********/LOG_USR.parquet") LIMIT 1')
            s3_columns = result.fetchall()
            
            print(f"{'#':<3} {'COLUMN_NAME':<25} {'DATA_TYPE':<15} {'NULLABLE':<8}")
            print("-" * 60)
            
            s3_schema = {}
            for i, col in enumerate(s3_columns, 1):
                # DuckDB DESCRIBE puede devolver diferentes números de columnas
                if len(col) >= 2:
                    col_name = col[0]
                    data_type = col[1]
                    nullable = col[2] if len(col) > 2 else 'YES'

                    s3_schema[col_name] = {
                        'type': data_type,
                        'nullable': nullable
                    }

                    nullable_str = 'Y' if nullable == 'YES' else 'N'
                    print(f"{i:<3} {col_name:<25} {data_type:<15} {nullable_str:<8}")
                else:
                    print(f"Error en columna {i}: {col}")
            
            print(f"\nTotal columnas S3: {len(s3_columns)}")
            
        except Exception as e:
            print(f"Error leyendo estructura S3: {e}")
            return
        
        # 3. Comparación de esquemas
        print("\n3. COMPARACIÓN DE ESQUEMAS:")
        print("=" * 80)
        
        # Verificar número de columnas
        if len(oracle_columns) == len(s3_columns):
            print(f"✅ NÚMERO DE COLUMNAS: {len(oracle_columns)} (IGUAL)")
        else:
            print(f"❌ NÚMERO DE COLUMNAS: Oracle {len(oracle_columns)} vs S3 {len(s3_columns)}")
        
        # Verificar nombres de columnas
        oracle_col_names = set(oracle_schema.keys())
        s3_col_names = set(s3_schema.keys())
        
        missing_in_s3 = oracle_col_names - s3_col_names
        extra_in_s3 = s3_col_names - oracle_col_names
        
        print(f"\n3.1 NOMBRES DE COLUMNAS:")
        if missing_in_s3:
            print(f"❌ Columnas faltantes en S3: {missing_in_s3}")
        if extra_in_s3:
            print(f"⚠️  Columnas extra en S3: {extra_in_s3}")
        if not missing_in_s3 and not extra_in_s3:
            print("✅ NOMBRES DE COLUMNAS: IDÉNTICOS")
        
        # 4. Comparación detallada por columna
        print(f"\n3.2 COMPARACIÓN DETALLADA POR COLUMNA:")
        print("-" * 80)
        print(f"{'COLUMN_NAME':<25} {'ORACLE_TYPE':<20} {'S3_TYPE':<20} {'STATUS':<10}")
        print("-" * 80)
        
        type_mismatches = []
        common_columns = oracle_col_names & s3_col_names
        
        for col_name in sorted(common_columns):
            oracle_type = oracle_schema[col_name]['type']
            s3_type = s3_schema[col_name]['type']
            
            # Mapeo de tipos Oracle a DuckDB
            oracle_to_duckdb = {
                'VARCHAR2': 'VARCHAR',
                'CHAR': 'VARCHAR',
                'NUMBER': 'DOUBLE',
                'DATE': 'TIMESTAMP',
                'TIMESTAMP': 'TIMESTAMP',
                'CLOB': 'VARCHAR'
            }
            
            expected_s3_type = oracle_to_duckdb.get(oracle_type, oracle_type)
            
            if s3_type == expected_s3_type or s3_type == 'VARCHAR':  # VARCHAR es compatible
                status = "✅"
            else:
                status = "❌"
                type_mismatches.append((col_name, oracle_type, s3_type))
            
            print(f"{col_name:<25} {oracle_type:<20} {s3_type:<20} {status:<10}")
        
        # 5. Resumen de validación
        print("\n4. RESUMEN DE VALIDACIÓN:")
        print("=" * 80)
        
        total_issues = len(missing_in_s3) + len(extra_in_s3) + len(type_mismatches)
        
        if total_issues == 0:
            print("🎉 VALIDACIÓN EXITOSA: ESTRUCTURA IDÉNTICA")
            print("✅ Número de columnas: IGUAL")
            print("✅ Nombres de columnas: IDÉNTICOS")
            print("✅ Tipos de datos: COMPATIBLES")
            print("\n🎯 CONCLUSIÓN: Pipeline S3 es 100% compatible con Oracle")
        else:
            print(f"⚠️  VALIDACIÓN PARCIAL: {total_issues} diferencias encontradas")
            
            if missing_in_s3:
                print(f"❌ {len(missing_in_s3)} columnas faltantes en S3")
            if extra_in_s3:
                print(f"⚠️  {len(extra_in_s3)} columnas extra en S3")
            if type_mismatches:
                print(f"❌ {len(type_mismatches)} tipos de datos incompatibles")
                print("\nTipos incompatibles:")
                for col, oracle_type, s3_type in type_mismatches:
                    print(f"   - {col}: Oracle {oracle_type} vs S3 {s3_type}")
        
        # 6. Verificación de datos de muestra
        print("\n5. VERIFICACIÓN DE DATOS DE MUESTRA:")
        print("-" * 50)
        
        # Muestra Oracle
        oracle_cursor.execute("""
            SELECT userHistId, createdOn, TipoDocumento, Documento, BankDomain, requestType
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            ORDER BY createdOn
            FETCH FIRST 3 ROWS ONLY
        """)
        oracle_sample = oracle_cursor.fetchall()
        
        print("Oracle (muestra):")
        for i, row in enumerate(oracle_sample, 1):
            print(f"   {i}. {row[0]} | {row[1]} | {row[2]} | {row[3]} | {row[4]} | {row[5]}")
        
        # Muestra S3
        try:
            result = duck_conn.sql('''
                SELECT userHistId, createdOn, TipoDocumento, Documento, BankDomain, requestType
                FROM read_parquet("S3_LOG_USER/output/********/LOG_USR.parquet")
                ORDER BY createdOn
                LIMIT 3
            ''')
            s3_sample = result.fetchall()
            
            print("\nS3 Pipeline (muestra):")
            for i, row in enumerate(s3_sample, 1):
                print(f"   {i}. {row[0]} | {row[1]} | {row[2]} | {row[3]} | {row[4]} | {row[5]}")
                
        except Exception as e:
            print(f"Error leyendo muestra S3: {e}")
        
        oracle_cursor.close()
        oracle_conn.close()
        duck_conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
