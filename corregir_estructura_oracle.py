#!/usr/bin/env python3
"""
Script para corregir la estructura del pipeline para que coincida exactamente con Oracle
"""

def corregir_pipeline():
    # Leer el archivo actual
    with open('S3_LOG_USER/pipeline_log_usuarios_duckdb.py', 'r') as f:
        content = f.read()
    
    # Mapeo de correcciones de nombres de columnas (camelCase → MAYÚSCULAS)
    column_corrections = {
        'userHistId': 'USERHISTID',
        'createdOn': 'CREATEDON',
        'TipoDocumento': 'TIPODOCUMENTO',
        'Documento': 'DOCUMENTO',
        'Msisdn': 'MSISDN',
        'MsisdnB': 'MSISDNB',
        'BankDomain': 'BANKDOMAIN',
        'created_by': 'CREATED_BY',
        'userId': 'USERID',
        'accountType': 'ACCOUNTTYPE',
        'accountId': 'ACCOUNTID',
        'Nombre': 'NOMBRE',
        'Apellido': 'APELLIDO',
        'NNombre': 'NNOMBRE',
        'NApellido': 'NAPELLIDO',
        'perfilA': 'PERFILA',
        'perfilB': 'PERFILB',
        'IdiomaA': 'IDIOMAA',
        'IdiomaB': 'IDIOMAB',
        'TelcoA': 'TELCOA',
        'TelcoB': 'TELCOB',
        'razon': 'RAZON',
        'PerfilCuenta': 'PERFILCUENTA',
        'PerfilCuentaA': 'PERFILCUENTAA',
        'perfilCuentaB': 'PERFILCUENTAB',
        'TipoDocumentoA': 'TIPODOCUMENTOA',
        'TipoDocumentoB': 'TIPODOCUMENTOB',
        'DocumentoB': 'DOCUMENTOB',
        'NumDocumentoB': 'NUMDOCUMENTOB',
        'requestType': 'REQUESTTYPE',
        'oldData': 'OLDDATA',
        'newData': 'NEWDATA',
        'useridold': 'USERIDOLD',
        'accountidold': 'ACCOUNTIDOLD'
    }
    
    # Aplicar correcciones de nombres de columnas
    for old_name, new_name in column_corrections.items():
        # Corregir en SELECT statements
        content = content.replace(f' AS {old_name}', f' AS {new_name}')
        content = content.replace(f' AS {old_name},', f' AS {new_name},')
    
    # Corregir tipos de datos
    type_corrections = [
        # Fechas deben ser TIMESTAMP
        ('umh.created_on AS CREATEDON', 'CAST(umh.created_on AS TIMESTAMP) AS CREATEDON'),
        ('uach.MODIFIED_ON AS CREATEDON', 'CAST(uach.MODIFIED_ON AS TIMESTAMP) AS CREATEDON'),
        ('ud.CREATED_ON AS CREATEDON', 'CAST(ud.CREATED_ON AS TIMESTAMP) AS CREATEDON'),
        ('ud.STATUS_CHANGE_ON AS CREATEDON', 'CAST(ud.STATUS_CHANGE_ON AS TIMESTAMP) AS CREATEDON'),
        
        # NULLs deben ser VARCHAR
        ('NULL AS MSISDNB', 'CAST(NULL AS VARCHAR) AS MSISDNB'),
        ('NULL AS NNOMBRE', 'CAST(NULL AS VARCHAR) AS NNOMBRE'),
        ('NULL AS NAPELLIDO', 'CAST(NULL AS VARCHAR) AS NAPELLIDO'),
        ('NULL AS PERFILB', 'CAST(NULL AS VARCHAR) AS PERFILB'),
        ('NULL AS IDIOMAB', 'CAST(NULL AS VARCHAR) AS IDIOMAB'),
        ('NULL AS TELCOB', 'CAST(NULL AS VARCHAR) AS TELCOB'),
        ('NULL AS PERFILCUENTAB', 'CAST(NULL AS VARCHAR) AS PERFILCUENTAB'),
        ('NULL AS TIPODOCUMENTOB', 'CAST(NULL AS VARCHAR) AS TIPODOCUMENTOB'),
        ('NULL AS NUMDOCUMENTOB', 'CAST(NULL AS VARCHAR) AS NUMDOCUMENTOB'),
        ('NULL AS ACCOUNTIDOLD', 'CAST(NULL AS VARCHAR) AS ACCOUNTIDOLD'),
        
        # CLOB fields deben ser VARCHAR
        ('umh.old_data AS OLDDATA', 'CAST(umh.old_data AS VARCHAR) AS OLDDATA'),
        ('umh.new_data AS NEWDATA', 'CAST(umh.new_data AS VARCHAR) AS NEWDATA'),
    ]
    
    for old_expr, new_expr in type_corrections:
        content = content.replace(old_expr, new_expr)
    
    # Escribir el archivo corregido
    with open('S3_LOG_USER/pipeline_log_usuarios_duckdb_fixed.py', 'w') as f:
        f.write(content)
    
    print("✅ Pipeline corregido guardado como: pipeline_log_usuarios_duckdb_fixed.py")
    print("📋 Correcciones aplicadas:")
    print(f"   - {len(column_corrections)} nombres de columnas corregidos")
    print(f"   - {len(type_corrections)} tipos de datos corregidos")
    print("   - Estructura compatible con Oracle LOG_USR")

if __name__ == "__main__":
    corregir_pipeline()
