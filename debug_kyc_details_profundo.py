#!/usr/bin/env python3
"""
Debug profundo de KYC_DETAILS para entender por qué tenemos valores None y vacíos
"""

import pandas as pd
import duckdb
import boto3

def analyze_kyc_details_deep():
    """Análisis profundo de KYC_DETAILS"""
    try:
        session = boto3.Session()
        conn = duckdb.connect()
        conn.execute("INSTALL httpfs;")
        conn.execute("LOAD httpfs;")
        
        credentials = session.get_credentials()
        conn.execute(f"""
        SET s3_region='us-east-1';
        SET s3_access_key_id='{credentials.access_key}';
        SET s3_secret_access_key='{credentials.secret_key}';
        """)
        
        if credentials.token:
            conn.execute(f"SET s3_session_token='{credentials.token}';")
        
        print("=== ANÁLISIS PROFUNDO KYC_DETAILS ===")
        
        kyc_path = "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/KYC_DETAILS_ORA/consolidado_puro.parquet"
        
        # Analizar distribución de ID_TYPE en KYC_DETAILS
        query1 = f"""
        SELECT 
            ID_TYPE,
            COUNT(*) as CANTIDAD,
            COUNT(CASE WHEN STATUS = 'A' THEN 1 END) as ACTIVOS,
            COUNT(CASE WHEN IS_PRIMARY_KYC_ID = 'Y' THEN 1 END) as PRIMARIOS
        FROM read_parquet('{kyc_path}')
        GROUP BY ID_TYPE
        ORDER BY CANTIDAD DESC
        """
        
        df_kyc_types = conn.execute(query1).df()
        print("Distribución ID_TYPE en KYC_DETAILS:")
        print(df_kyc_types.to_string(index=False))
        
        # Verificar registros con ID_TYPE NULL o vacío
        query2 = f"""
        SELECT 
            COUNT(*) as TOTAL_KYC,
            COUNT(CASE WHEN ID_TYPE IS NULL THEN 1 END) as NULL_ID_TYPE,
            COUNT(CASE WHEN ID_TYPE = '' THEN 1 END) as EMPTY_ID_TYPE,
            COUNT(CASE WHEN ID_VALUE IS NULL THEN 1 END) as NULL_ID_VALUE,
            COUNT(CASE WHEN ID_VALUE = '' THEN 1 END) as EMPTY_ID_VALUE
        FROM read_parquet('{kyc_path}')
        """
        
        df_kyc_nulls = conn.execute(query2).df()
        print(f"\nAnálisis de valores NULL/vacíos en KYC_DETAILS:")
        print(df_kyc_nulls.to_string(index=False))
        
        # Ejemplos de registros problemáticos
        query3 = f"""
        SELECT 
            KYC_ID,
            ID_TYPE,
            ID_VALUE,
            STATUS,
            IS_PRIMARY_KYC_ID
        FROM read_parquet('{kyc_path}')
        WHERE ID_TYPE IS NULL OR ID_TYPE = ''
        LIMIT 10
        """
        
        df_problematic = conn.execute(query3).df()
        print(f"\nEjemplos de registros con ID_TYPE problemático:")
        print(df_problematic.to_string(index=False))
        
        return df_kyc_types
        
    except Exception as e:
        print(f"Error analizando KYC_DETAILS: {e}")
        return None

def analyze_user_data_trx_kyc_join():
    """Analizar el JOIN entre USER_DATA_TRX y KYC"""
    try:
        conn = duckdb.connect()
        
        user_data_path = "S3_LOG_USER/TEMP_LOGS_USUARIOS/20250603/USER_DATA_TRX.parquet"
        
        print("\n=== ANÁLISIS USER_DATA_TRX - VALORES ID_TYPE ===")
        
        # Analizar distribución de ID_TYPE en USER_DATA_TRX
        query = f"""
        SELECT 
            ID_TYPE,
            COUNT(*) as CANTIDAD,
            MIN(ID_VALUE) as MIN_ID_VALUE,
            MAX(ID_VALUE) as MAX_ID_VALUE
        FROM read_parquet('{user_data_path}')
        GROUP BY ID_TYPE
        ORDER BY CANTIDAD DESC
        """
        
        df_user_data = conn.execute(query).df()
        print("Distribución ID_TYPE en USER_DATA_TRX:")
        print(df_user_data.to_string(index=False))
        
        # Ejemplos de registros con ID_TYPE problemático
        query2 = f"""
        SELECT 
            O_USER_ID,
            ID_TYPE,
            ID_VALUE,
            PROFILE,
            ISSUER_CODE
        FROM read_parquet('{user_data_path}')
        WHERE ID_TYPE IS NULL OR ID_TYPE = ''
        LIMIT 10
        """
        
        df_problematic = conn.execute(query2).df()
        print(f"\nEjemplos de USER_DATA_TRX con ID_TYPE problemático:")
        print(df_problematic.to_string(index=False))
        
        return df_user_data
        
    except Exception as e:
        print(f"Error analizando USER_DATA_TRX: {e}")
        return None

def test_oracle_exact_logic():
    """Probar la lógica EXACTA de Oracle paso a paso"""
    try:
        session = boto3.Session()
        conn = duckdb.connect()
        conn.execute("INSTALL httpfs;")
        conn.execute("LOAD httpfs;")
        
        credentials = session.get_credentials()
        conn.execute(f"""
        SET s3_region='us-east-1';
        SET s3_access_key_id='{credentials.access_key}';
        SET s3_secret_access_key='{credentials.secret_key}';
        """)
        
        if credentials.token:
            conn.execute(f"SET s3_session_token='{credentials.token}';")
        
        print("\n=== PRUEBA LÓGICA EXACTA ORACLE ===")
        
        user_profile_path = "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/USER_PROFILE_ORA/consolidado_puro.parquet"
        kyc_details_path = "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/KYC_DETAILS_ORA/consolidado_puro.parquet"
        
        # Paso 1: Verificar USER_PROFILE con KYC_ID
        query1 = f"""
        SELECT 
            COUNT(*) as TOTAL_USERS,
            COUNT(KYC_ID) as USERS_WITH_KYC_ID,
            COUNT(CASE WHEN KYC_ID IS NOT NULL AND KYC_ID != '' THEN 1 END) as USERS_WITH_VALID_KYC_ID
        FROM read_parquet('{user_profile_path}')
        WHERE CREATED_ON <= '2025-06-03'
        """
        
        df_step1 = conn.execute(query1).df()
        print("Paso 1 - USER_PROFILE con KYC_ID:")
        print(df_step1.to_string(index=False))
        
        # Paso 2: INNER JOIN con KYC_DETAILS
        query2 = f"""
        SELECT 
            COUNT(*) as TOTAL_AFTER_JOIN,
            COUNT(UK.ID_TYPE) as WITH_ID_TYPE,
            COUNT(CASE WHEN UK.ID_TYPE = 'DNI' THEN 1 END) as DNI_COUNT,
            COUNT(CASE WHEN UK.ID_TYPE = 'CE' THEN 1 END) as CE_COUNT,
            COUNT(CASE WHEN UK.ID_TYPE = 'RUC' THEN 1 END) as RUC_COUNT,
            COUNT(CASE WHEN UK.ID_TYPE IS NULL OR UK.ID_TYPE = '' THEN 1 END) as NULL_EMPTY_COUNT
        FROM read_parquet('{user_profile_path}') UP
        INNER JOIN read_parquet('{kyc_details_path}') UK ON UP.KYC_ID = UK.KYC_ID
        WHERE UP.CREATED_ON <= '2025-06-03'
        """
        
        df_step2 = conn.execute(query2).df()
        print("\nPaso 2 - Después de INNER JOIN con KYC_DETAILS:")
        print(df_step2.to_string(index=False))
        
        # Paso 3: Ejemplos de registros después del JOIN
        query3 = f"""
        SELECT 
            UP.USER_ID,
            UP.KYC_ID,
            UK.ID_TYPE,
            UK.ID_VALUE,
            UK.STATUS,
            UK.IS_PRIMARY_KYC_ID
        FROM read_parquet('{user_profile_path}') UP
        INNER JOIN read_parquet('{kyc_details_path}') UK ON UP.KYC_ID = UK.KYC_ID
        WHERE UP.CREATED_ON <= '2025-06-03'
        AND (UK.ID_TYPE IS NULL OR UK.ID_TYPE = '')
        LIMIT 10
        """
        
        df_step3 = conn.execute(query3).df()
        print(f"\nPaso 3 - Ejemplos de registros con ID_TYPE problemático después del JOIN:")
        print(df_step3.to_string(index=False))
        
        return df_step2
        
    except Exception as e:
        print(f"Error probando lógica Oracle: {e}")
        return None

def main():
    """Función principal"""
    print("Debug profundo de KYC_DETAILS")
    print("="*60)
    
    # Analizar KYC_DETAILS
    analyze_kyc_details_deep()
    
    # Analizar USER_DATA_TRX actual
    analyze_user_data_trx_kyc_join()
    
    # Probar lógica exacta de Oracle
    test_oracle_exact_logic()
    
    print("\n" + "="*60)
    print("CONCLUSIÓN:")
    print("Identificar por qué el INNER JOIN con KYC_DETAILS")
    print("está produciendo registros con ID_TYPE NULL/vacío")

if __name__ == "__main__":
    main()
