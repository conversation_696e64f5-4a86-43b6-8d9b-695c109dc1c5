#!/usr/bin/env python3
"""
Buscar tabla USER_IDENTIFIER o alternativa para mapear AUTHENTICATION_ID
"""

import boto3
import duckdb

def search_user_identifier_s3():
    """Buscar USER_IDENTIFIER en S3"""
    try:
        session = boto3.Session()
        conn = duckdb.connect()
        conn.execute("INSTALL httpfs;")
        conn.execute("LOAD httpfs;")
        
        credentials = session.get_credentials()
        conn.execute(f"""
        SET s3_region='us-east-1';
        SET s3_access_key_id='{credentials.access_key}';
        SET s3_secret_access_key='{credentials.secret_key}';
        """)
        
        if credentials.token:
            conn.execute(f"SET s3_session_token='{credentials.token}';")
        
        print("=== BUSCANDO USER_IDENTIFIER EN S3 ===")
        
        # Intentar acceder a USER_IDENTIFIER
        possible_paths = [
            "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/USER_IDENTIFIER_ORA/consolidado_puro.parquet",
            "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/USER_IDENTIFIER/consolidado_puro.parquet"
        ]
        
        for path in possible_paths:
            try:
                print(f"Intentando: {path}")
                result = conn.execute(f"SELECT COUNT(*) FROM read_parquet('{path}')").fetchone()
                print(f"✅ ENCONTRADO: {result[0]} registros en {path}")
                
                # Mostrar esquema
                schema = conn.execute(f"DESCRIBE SELECT * FROM read_parquet('{path}') LIMIT 1").df()
                print("Columnas disponibles:")
                for col in schema['column_name']:
                    print(f"  - {col}")
                
                # Mostrar algunos ejemplos
                sample = conn.execute(f"SELECT * FROM read_parquet('{path}') LIMIT 5").df()
                print("\nEjemplos de datos:")
                print(sample.to_string(index=False))
                
                return path
                
            except Exception as e:
                print(f"❌ No encontrado: {e}")
        
        return None
        
    except Exception as e:
        print(f"Error buscando USER_IDENTIFIER: {e}")
        return None

def analyze_auth_id_patterns():
    """Analizar patrones en AUTHENTICATION_ID para encontrar mapeo alternativo"""
    try:
        conn = duckdb.connect()
        
        auth_change_path = "S3_LOG_USER/TEMP_LOGS_USUARIOS/20250603/USER_AUTH_CHANGE_HISTORY.parquet"
        user_data_path = "S3_LOG_USER/TEMP_LOGS_USUARIOS/20250603/USER_DATA_TRX.parquet"
        
        print("\n=== ANÁLISIS PATRONES AUTHENTICATION_ID ===")
        
        # Analizar patrones en AUTHENTICATION_ID
        query = f"""
        SELECT 
            CASE 
                WHEN AUTHENTICATION_ID LIKE 'AU.SU%' THEN 'AU.SU Pattern'
                WHEN AUTHENTICATION_ID LIKE 'AU.%' THEN 'AU.Number Pattern'
                ELSE 'Other Pattern'
            END as PATTERN_TYPE,
            COUNT(*) as CANTIDAD,
            MIN(AUTHENTICATION_ID) as EJEMPLO
        FROM read_parquet('{auth_change_path}')
        GROUP BY 1
        ORDER BY CANTIDAD DESC
        """
        
        df_patterns = conn.execute(query).df()
        print("Patrones en AUTHENTICATION_ID:")
        print(df_patterns.to_string(index=False))
        
        # Intentar extraer USER_ID de AUTHENTICATION_ID
        query2 = f"""
        SELECT 
            AUTHENTICATION_ID,
            CASE 
                WHEN AUTHENTICATION_ID LIKE 'AU.SU%' THEN REPLACE(AUTHENTICATION_ID, 'AU.SU', 'SU')
                WHEN AUTHENTICATION_ID LIKE 'AU.%' THEN REPLACE(AUTHENTICATION_ID, 'AU.', '')
                ELSE AUTHENTICATION_ID
            END as EXTRACTED_USER_ID
        FROM read_parquet('{auth_change_path}')
        LIMIT 10
        """
        
        df_extraction = conn.execute(query2).df()
        print(f"\nIntento de extracción USER_ID:")
        print(df_extraction.to_string(index=False))
        
        # Verificar si los USER_ID extraídos existen en USER_DATA_TRX
        query3 = f"""
        WITH EXTRACTED_IDS AS (
            SELECT 
                AUTHENTICATION_ID,
                CASE 
                    WHEN AUTHENTICATION_ID LIKE 'AU.SU%' THEN REPLACE(AUTHENTICATION_ID, 'AU.SU', 'SU')
                    WHEN AUTHENTICATION_ID LIKE 'AU.%' THEN REPLACE(AUTHENTICATION_ID, 'AU.', '')
                    ELSE AUTHENTICATION_ID
                END as EXTRACTED_USER_ID
            FROM read_parquet('{auth_change_path}')
            LIMIT 100
        )
        SELECT 
            COUNT(*) as TOTAL_EXTRACTED,
            COUNT(UD.O_USER_ID) as MATCHED_IN_USER_DATA
        FROM EXTRACTED_IDS EI
        LEFT JOIN read_parquet('{user_data_path}') UD ON EI.EXTRACTED_USER_ID = UD.O_USER_ID
        """
        
        df_match_test = conn.execute(query3).df()
        print(f"\nTest de match con USER_DATA_TRX:")
        print(df_match_test.to_string(index=False))
        
        return df_patterns
        
    except Exception as e:
        print(f"Error analizando patrones: {e}")
        return None

def propose_fix():
    """Proponer solución para el problema"""
    print("\n=== PROPUESTA DE CORRECCIÓN ===")
    print("OPCIONES IDENTIFICADAS:")
    print("1. Si USER_IDENTIFIER existe en S3: Agregar JOIN con esta tabla")
    print("2. Si no existe: Usar lógica de extracción de USER_ID desde AUTHENTICATION_ID")
    print("3. Como fallback: Usar valores por defecto para registros sin match")
    print()
    print("IMPLEMENTACIÓN RECOMENDADA:")
    print("- Modificar el pipeline para incluir el mapeo correcto")
    print("- Asegurar que todos los registros AUTH_CHANGE tengan TIPODOCUMENTO válido")

def main():
    """Función principal"""
    print("Buscando solución para mapeo AUTHENTICATION_ID")
    print("="*60)
    
    # Buscar USER_IDENTIFIER
    user_identifier_path = search_user_identifier_s3()
    
    # Analizar patrones alternativos
    analyze_auth_id_patterns()
    
    # Proponer solución
    propose_fix()
    
    return user_identifier_path

if __name__ == "__main__":
    main()
