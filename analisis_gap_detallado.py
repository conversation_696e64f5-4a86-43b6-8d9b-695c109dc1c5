#!/usr/bin/env python3
import oracledb
import duckdb
import boto3

def main():
    try:
        # Conexión a Oracle
        oracle_conn = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        oracle_cursor = oracle_conn.cursor()
        
        # Configurar DuckDB
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        duck_conn = duckdb.connect()
        duck_conn.sql('INSTALL httpfs;')
        duck_conn.sql('LOAD httpfs;')
        duck_conn.sql('SET s3_region=\'us-east-1\';')
        duck_conn.sql('SET s3_use_ssl=true;')
        duck_conn.sql('SET s3_url_style=\'path\';')
        duck_conn.sql(f'SET s3_access_key_id=\'{credentials.access_key}\';')
        duck_conn.sql(f'SET s3_secret_access_key=\'{credentials.secret_key}\';')
        if credentials.token:
            duck_conn.sql(f'SET s3_session_token=\'{credentials.token}\';')
        
        print("=== ANÁLISIS DETALLADO DE GAP - DATA ENGINEERING ===")
        print("Fecha: 2025-06-03")
        print("=" * 80)
        
        # 1. Obtener distribución exacta de Oracle
        print("\n1. DISTRIBUCIÓN ORACLE (REFERENCIA):")
        oracle_cursor.execute("""
            SELECT requestType, COUNT(*) as cantidad
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            GROUP BY requestType
            ORDER BY COUNT(*) DESC
        """)
        oracle_types = oracle_cursor.fetchall()
        oracle_dict = {row[0]: row[1] for row in oracle_types}
        oracle_total = sum(oracle_dict.values())
        
        for row in oracle_types:
            print(f"   {row[0]}: {row[1]:,}")
        print(f"   TOTAL ORACLE: {oracle_total:,}")
        
        # 2. Obtener distribución de nuestro pipeline
        print("\n2. DISTRIBUCIÓN NUESTRO PIPELINE:")
        try:
            result = duck_conn.sql('''
                SELECT requestType, COUNT(*) as cantidad
                FROM read_parquet("output/20250603/LOG_USR.parquet")
                GROUP BY requestType
                ORDER BY cantidad DESC
            ''')
            our_types = result.fetchall()
            our_dict = {row[0]: row[1] for row in our_types}
            our_total = sum(our_dict.values())
            
            for row in our_types:
                print(f"   {row[0]}: {row[1]:,}")
            print(f"   TOTAL NUESTRO: {our_total:,}")
            
        except Exception as e:
            print(f"   Error: {e}")
            return
        
        # 3. Análisis de GAP por tipo
        print("\n3. ANÁLISIS DE GAP POR REQUEST_TYPE:")
        print("-" * 80)
        print(f"{'REQUEST_TYPE':<25} {'ORACLE':<10} {'NUESTRO':<10} {'GAP':<10} {'%':<8}")
        print("-" * 80)
        
        total_gap = 0
        all_types = set(oracle_dict.keys()) | set(our_dict.keys())
        
        for request_type in sorted(all_types):
            oracle_count = oracle_dict.get(request_type, 0)
            our_count = our_dict.get(request_type, 0)
            gap = oracle_count - our_count
            percentage = (our_count / oracle_count * 100) if oracle_count > 0 else 0
            
            total_gap += gap
            status = "✅" if gap == 0 else "❌" if gap > 0 else "⚠️"
            
            print(f"{request_type:<25} {oracle_count:<10,} {our_count:<10,} {gap:<10,} {percentage:<7.1f}% {status}")
        
        print("-" * 80)
        print(f"{'TOTAL':<25} {oracle_total:<10,} {our_total:<10,} {total_gap:<10,} {(our_total/oracle_total*100):<7.1f}%")
        
        # 4. Identificar tipos faltantes
        print("\n4. TIPOS FALTANTES EN NUESTRO PIPELINE:")
        missing_types = set(oracle_dict.keys()) - set(our_dict.keys())
        if missing_types:
            for missing_type in sorted(missing_types):
                count = oracle_dict[missing_type]
                print(f"   ❌ {missing_type}: {count:,} registros faltantes")
        else:
            print("   ✅ No hay tipos completamente faltantes")
        
        # 5. Análisis de CHANGE_AUTH_FACTOR detallado
        print("\n5. ANÁLISIS DETALLADO CHANGE_AUTH_FACTOR:")
        
        # Oracle CHANGE_AUTH_FACTOR
        oracle_cursor.execute("""
            SELECT COUNT(*) 
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            AND requestType = 'CHANGE_AUTH_FACTOR'
        """)
        oracle_change_auth = oracle_cursor.fetchone()[0]
        
        # Datos origen USER_AUTH_CHANGE_HISTORY
        oracle_cursor.execute("""
            SELECT COUNT(*) 
            FROM PDP_PROD10_MAINDB.USER_AUTH_CHANGE_HISTORY
            WHERE TRUNC(MODIFIED_ON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            AND AUTHENTICATION_TYPE = 'PIN'
        """)
        oracle_auth_origen = oracle_cursor.fetchone()[0]
        
        # S3 USER_AUTH_CHANGE_HISTORY
        result = duck_conn.sql('''
            SELECT COUNT(*) 
            FROM read_parquet("s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/USER_AUTH_CHANGE_HISTORY_ORA/consolidado_puro.parquet")
            WHERE CAST(MODIFIED_ON AS DATE) = CAST('2025-06-03' AS DATE)
            AND AUTHENTICATION_TYPE = 'PIN'
        ''')
        s3_auth_origen = result.fetchone()[0]
        
        # Nuestro pipeline CHANGE_AUTH_FACTOR
        our_change_auth = our_dict.get('CHANGE_AUTH_FACTOR', 0)
        
        print(f"   Oracle LOG_USR CHANGE_AUTH_FACTOR: {oracle_change_auth:,}")
        print(f"   Oracle origen USER_AUTH_CHANGE_HISTORY: {oracle_auth_origen:,}")
        print(f"   S3 origen USER_AUTH_CHANGE_HISTORY: {s3_auth_origen:,}")
        print(f"   Nuestro pipeline CHANGE_AUTH_FACTOR: {our_change_auth:,}")
        print(f"   Factor multiplicador Oracle: {oracle_change_auth/oracle_auth_origen:.2f}x")
        
        # 6. Análisis de User Modification detallado
        print("\n6. ANÁLISIS DETALLADO USER MODIFICATION:")
        
        # Oracle User Modification
        oracle_user_mod = oracle_dict.get('User Modification', 0)
        
        # Datos origen USER_MODIFICATION_HISTORY
        oracle_cursor.execute("""
            SELECT COUNT(*) 
            FROM PDP_PROD10_MAINDB.USER_MODIFICATION_HISTORY
            WHERE TRUNC(CREATED_ON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
        """)
        oracle_mod_origen = oracle_cursor.fetchone()[0]
        
        # S3 USER_MODIFICATION_HISTORY
        result = duck_conn.sql('''
            SELECT COUNT(*) 
            FROM read_parquet("s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/USER_MODIFICATION_HISTORY_ORA/consolidado_puro.parquet")
            WHERE CAST(CREATED_ON AS DATE) = CAST('2025-06-03' AS DATE)
        ''')
        s3_mod_origen = result.fetchone()[0]
        
        # Nuestro pipeline User Modification
        our_user_mod = our_dict.get('User Modification', 0)
        
        print(f"   Oracle LOG_USR User Modification: {oracle_user_mod:,}")
        print(f"   Oracle origen USER_MODIFICATION_HISTORY: {oracle_mod_origen:,}")
        print(f"   S3 origen USER_MODIFICATION_HISTORY: {s3_mod_origen:,}")
        print(f"   Nuestro pipeline User Modification: {our_user_mod:,}")
        
        if oracle_mod_origen > 0:
            print(f"   Diferencia origen Oracle vs S3: {oracle_mod_origen - s3_mod_origen:,}")
            print(f"   Factor Oracle: {oracle_user_mod/oracle_mod_origen:.2f}x")
        
        # 7. Recomendaciones
        print("\n7. RECOMENDACIONES PARA CERRAR EL GAP:")
        print("=" * 80)
        
        if total_gap > 0:
            print(f"   GAP TOTAL: {total_gap:,} registros ({(total_gap/oracle_total*100):.1f}%)")
            print("\n   ACCIONES REQUERIDAS:")
            
            # Identificar las principales diferencias
            major_gaps = [(k, oracle_dict[k] - our_dict.get(k, 0)) 
                         for k in oracle_dict.keys() 
                         if oracle_dict[k] - our_dict.get(k, 0) > 100]
            
            for request_type, gap in sorted(major_gaps, key=lambda x: x[1], reverse=True):
                print(f"   1. {request_type}: Falta {gap:,} registros")
                
                if request_type == 'CHANGE_AUTH_FACTOR':
                    factor_needed = oracle_change_auth / s3_auth_origen if s3_auth_origen > 0 else 0
                    print(f"      - Necesita factor {factor_needed:.2f}x sobre datos S3")
                    print(f"      - Revisar lógica de multiplicación en SP_LOG_USR")
                
                elif request_type == 'User Modification':
                    print(f"      - Verificar si faltan tipos de USER_MODIFICATION_HISTORY")
                    print(f"      - Revisar filtros de fecha en consulta")
        
        else:
            print("   ✅ NO HAY GAP - PIPELINE PERFECTO")
        
        oracle_cursor.close()
        oracle_conn.close()
        duck_conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
