#!/usr/bin/env python3
import oracledb
import duckdb
import boto3
import re

def main():
    try:
        # Conexión a Oracle
        oracle_conn = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        oracle_cursor = oracle_conn.cursor()
        
        # Configurar DuckDB
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        duck_conn = duckdb.connect()
        duck_conn.sql('INSTALL httpfs;')
        duck_conn.sql('LOAD httpfs;')
        duck_conn.sql('SET s3_region=\'us-east-1\';')
        duck_conn.sql('SET s3_use_ssl=true;')
        duck_conn.sql('SET s3_url_style=\'path\';')
        duck_conn.sql(f'SET s3_access_key_id=\'{credentials.access_key}\';')
        duck_conn.sql(f'SET s3_secret_access_key=\'{credentials.secret_key}\';')
        if credentials.token:
            duck_conn.sql(f'SET s3_session_token=\'{credentials.token}\';')
        
        print("=== VALIDACIÓN TIPOS DE INFORMACIÓN - DATA ENGINEERING ===")
        print("Análisis columna por columna: Oracle vs Pipeline S3")
        print("=" * 80)
        
        # 1. USERHISTID - Patrones de IDs
        print("\n1. USERHISTID - Patrones de identificadores:")
        print("-" * 60)
        
        # Oracle USERHISTID
        oracle_cursor.execute("""
            SELECT 
                SUBSTR(USERHISTID, 1, 3) as prefix,
                COUNT(*) as cantidad,
                MIN(USERHISTID) as ejemplo_min,
                MAX(USERHISTID) as ejemplo_max
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            GROUP BY SUBSTR(USERHISTID, 1, 3)
            ORDER BY COUNT(*) DESC
        """)
        oracle_userhistid = oracle_cursor.fetchall()
        
        print("Oracle USERHISTID:")
        for row in oracle_userhistid:
            print(f"   {row[0]}: {row[1]:,} registros (ej: {row[2]} ... {row[3]})")
        
        # S3 USERHISTID
        try:
            result = duck_conn.sql('''
                SELECT 
                    SUBSTR(USERHISTID, 1, 3) as prefix,
                    COUNT(*) as cantidad,
                    MIN(USERHISTID) as ejemplo_min,
                    MAX(USERHISTID) as ejemplo_max
                FROM read_parquet("output/********/LOG_USR.parquet")
                GROUP BY SUBSTR(USERHISTID, 1, 3)
                ORDER BY COUNT(*) DESC
            ''')
            s3_userhistid = result.fetchall()
            
            print("\nS3 USERHISTID:")
            for row in s3_userhistid:
                print(f"   {row[0]}: {row[1]:,} registros (ej: {row[2]} ... {row[3]})")
                
        except Exception as e:
            print(f"Error S3 USERHISTID: {e}")
        
        # 2. TIPODOCUMENTO - Tipos de documento
        print("\n2. TIPODOCUMENTO - Tipos de documento:")
        print("-" * 60)
        
        # Oracle TIPODOCUMENTO
        oracle_cursor.execute("""
            SELECT TIPODOCUMENTO, COUNT(*) as cantidad
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            GROUP BY TIPODOCUMENTO
            ORDER BY COUNT(*) DESC
        """)
        oracle_tipodoc = oracle_cursor.fetchall()
        
        print("Oracle TIPODOCUMENTO:")
        for row in oracle_tipodoc:
            print(f"   '{row[0]}': {row[1]:,} registros")
        
        # S3 TIPODOCUMENTO
        try:
            result = duck_conn.sql('''
                SELECT TIPODOCUMENTO, COUNT(*) as cantidad
                FROM read_parquet("output/********/LOG_USR.parquet")
                GROUP BY TIPODOCUMENTO
                ORDER BY COUNT(*) DESC
            ''')
            s3_tipodoc = result.fetchall()
            
            print("\nS3 TIPODOCUMENTO:")
            for row in s3_tipodoc:
                print(f"   '{row[0]}': {row[1]:,} registros")
                
        except Exception as e:
            print(f"Error S3 TIPODOCUMENTO: {e}")
        
        # 3. DOCUMENTO - Números de documento
        print("\n3. DOCUMENTO - Números de documento:")
        print("-" * 60)
        
        # Oracle DOCUMENTO - patrones
        oracle_cursor.execute("""
            SELECT 
                LENGTH(DOCUMENTO) as longitud,
                COUNT(*) as cantidad,
                MIN(DOCUMENTO) as ejemplo_min,
                MAX(DOCUMENTO) as ejemplo_max
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            AND DOCUMENTO IS NOT NULL
            GROUP BY LENGTH(DOCUMENTO)
            ORDER BY COUNT(*) DESC
        """)
        oracle_documento = oracle_cursor.fetchall()
        
        print("Oracle DOCUMENTO (por longitud):")
        for row in oracle_documento:
            print(f"   {row[0]} dígitos: {row[1]:,} registros (ej: {row[2]} ... {row[3]})")
        
        # S3 DOCUMENTO
        try:
            result = duck_conn.sql('''
                SELECT 
                    LENGTH(DOCUMENTO) as longitud,
                    COUNT(*) as cantidad,
                    MIN(DOCUMENTO) as ejemplo_min,
                    MAX(DOCUMENTO) as ejemplo_max
                FROM read_parquet("output/********/LOG_USR.parquet")
                WHERE DOCUMENTO IS NOT NULL AND DOCUMENTO != ''
                GROUP BY LENGTH(DOCUMENTO)
                ORDER BY COUNT(*) DESC
            ''')
            s3_documento = result.fetchall()
            
            print("\nS3 DOCUMENTO (por longitud):")
            for row in s3_documento:
                print(f"   {row[0]} dígitos: {row[1]:,} registros (ej: {row[2]} ... {row[3]})")
                
        except Exception as e:
            print(f"Error S3 DOCUMENTO: {e}")
        
        # 4. MSISDN - Números de teléfono
        print("\n4. MSISDN - Números de teléfono:")
        print("-" * 60)
        
        # Oracle MSISDN
        oracle_cursor.execute("""
            SELECT 
                SUBSTR(MSISDN, 1, 2) as prefix,
                LENGTH(MSISDN) as longitud,
                COUNT(*) as cantidad,
                MIN(MSISDN) as ejemplo_min
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            AND MSISDN IS NOT NULL
            GROUP BY SUBSTR(MSISDN, 1, 2), LENGTH(MSISDN)
            ORDER BY COUNT(*) DESC
        """)
        oracle_msisdn = oracle_cursor.fetchall()
        
        print("Oracle MSISDN (por prefijo y longitud):")
        for row in oracle_msisdn[:10]:  # Top 10
            print(f"   {row[0]}xxxxxxx ({row[1]} dígitos): {row[2]:,} registros (ej: {row[3]})")
        
        # S3 MSISDN
        try:
            result = duck_conn.sql('''
                SELECT 
                    SUBSTR(MSISDN, 1, 2) as prefix,
                    LENGTH(MSISDN) as longitud,
                    COUNT(*) as cantidad,
                    MIN(MSISDN) as ejemplo_min
                FROM read_parquet("output/********/LOG_USR.parquet")
                WHERE MSISDN IS NOT NULL AND MSISDN != ''
                GROUP BY SUBSTR(MSISDN, 1, 2), LENGTH(MSISDN)
                ORDER BY COUNT(*) DESC
            ''')
            s3_msisdn = result.fetchall()
            
            print("\nS3 MSISDN (por prefijo y longitud):")
            for row in s3_msisdn[:10]:  # Top 10
                print(f"   {row[0]}xxxxxxx ({row[1]} dígitos): {row[2]:,} registros (ej: {row[3]})")
                
        except Exception as e:
            print(f"Error S3 MSISDN: {e}")
        
        # 5. BANKDOMAIN - Dominios bancarios
        print("\n5. BANKDOMAIN - Dominios bancarios:")
        print("-" * 60)
        
        # Oracle BANKDOMAIN
        oracle_cursor.execute("""
            SELECT 
                CASE WHEN BANKDOMAIN IS NULL OR BANKDOMAIN = '' THEN '[EMPTY]' ELSE BANKDOMAIN END as domain,
                COUNT(*) as cantidad,
                ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as porcentaje
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            GROUP BY BANKDOMAIN
            ORDER BY COUNT(*) DESC
        """)
        oracle_bankdomain = oracle_cursor.fetchall()
        
        print("Oracle BANKDOMAIN:")
        for row in oracle_bankdomain:
            print(f"   {row[0]}: {row[1]:,} registros ({row[2]}%)")
        
        # S3 BANKDOMAIN
        try:
            result = duck_conn.sql('''
                SELECT 
                    CASE WHEN BANKDOMAIN IS NULL OR BANKDOMAIN = '' THEN '[EMPTY]' ELSE BANKDOMAIN END as domain,
                    COUNT(*) as cantidad,
                    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as porcentaje
                FROM read_parquet("output/********/LOG_USR.parquet")
                GROUP BY BANKDOMAIN
                ORDER BY COUNT(*) DESC
            ''')
            s3_bankdomain = result.fetchall()
            
            print("\nS3 BANKDOMAIN:")
            for row in s3_bankdomain:
                print(f"   {row[0]}: {row[1]:,} registros ({row[2]}%)")
                
        except Exception as e:
            print(f"Error S3 BANKDOMAIN: {e}")
        
        # 6. USERID - Identificadores de usuario
        print("\n6. USERID - Identificadores de usuario:")
        print("-" * 60)
        
        # Oracle USERID
        oracle_cursor.execute("""
            SELECT 
                SUBSTR(USERID, 1, 3) as prefix,
                LENGTH(USERID) as longitud,
                COUNT(*) as cantidad,
                MIN(USERID) as ejemplo
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            AND USERID IS NOT NULL
            GROUP BY SUBSTR(USERID, 1, 3), LENGTH(USERID)
            ORDER BY COUNT(*) DESC
        """)
        oracle_userid = oracle_cursor.fetchall()
        
        print("Oracle USERID (por prefijo y longitud):")
        for row in oracle_userid[:5]:  # Top 5
            print(f"   {row[0]}xxx ({row[1]} chars): {row[2]:,} registros (ej: {row[3]})")
        
        # S3 USERID
        try:
            result = duck_conn.sql('''
                SELECT 
                    SUBSTR(USERID, 1, 3) as prefix,
                    LENGTH(USERID) as longitud,
                    COUNT(*) as cantidad,
                    MIN(USERID) as ejemplo
                FROM read_parquet("output/********/LOG_USR.parquet")
                WHERE USERID IS NOT NULL AND USERID != ''
                GROUP BY SUBSTR(USERID, 1, 3), LENGTH(USERID)
                ORDER BY COUNT(*) DESC
            ''')
            s3_userid = result.fetchall()
            
            print("\nS3 USERID (por prefijo y longitud):")
            for row in s3_userid[:5]:  # Top 5
                print(f"   {row[0]}xxx ({row[1]} chars): {row[2]:,} registros (ej: {row[3]})")
                
        except Exception as e:
            print(f"Error S3 USERID: {e}")
        
        # 7. ACCOUNTID - Identificadores de cuenta
        print("\n7. ACCOUNTID - Identificadores de cuenta:")
        print("-" * 60)
        
        # Oracle ACCOUNTID
        oracle_cursor.execute("""
            SELECT 
                SUBSTR(ACCOUNTID, 1, 3) as prefix,
                LENGTH(ACCOUNTID) as longitud,
                COUNT(*) as cantidad,
                MIN(ACCOUNTID) as ejemplo
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            AND ACCOUNTID IS NOT NULL
            GROUP BY SUBSTR(ACCOUNTID, 1, 3), LENGTH(ACCOUNTID)
            ORDER BY COUNT(*) DESC
        """)
        oracle_accountid = oracle_cursor.fetchall()
        
        print("Oracle ACCOUNTID (por prefijo y longitud):")
        for row in oracle_accountid[:5]:  # Top 5
            print(f"   {row[0]}xxx ({row[1]} chars): {row[2]:,} registros (ej: {row[3]})")
        
        # S3 ACCOUNTID
        try:
            result = duck_conn.sql('''
                SELECT 
                    SUBSTR(ACCOUNTID, 1, 3) as prefix,
                    LENGTH(ACCOUNTID) as longitud,
                    COUNT(*) as cantidad,
                    MIN(ACCOUNTID) as ejemplo
                FROM read_parquet("output/********/LOG_USR.parquet")
                WHERE ACCOUNTID IS NOT NULL AND ACCOUNTID != ''
                GROUP BY SUBSTR(ACCOUNTID, 1, 3), LENGTH(ACCOUNTID)
                ORDER BY COUNT(*) DESC
            ''')
            s3_accountid = result.fetchall()
            
            print("\nS3 ACCOUNTID (por prefijo y longitud):")
            for row in s3_accountid[:5]:  # Top 5
                print(f"   {row[0]}xxx ({row[1]} chars): {row[2]:,} registros (ej: {row[3]})")
                
        except Exception as e:
            print(f"Error S3 ACCOUNTID: {e}")
        
        # 8. REQUESTTYPE - Tipos de request
        print("\n8. REQUESTTYPE - Tipos de request:")
        print("-" * 60)
        
        # Oracle REQUESTTYPE
        oracle_cursor.execute("""
            SELECT REQUESTTYPE, COUNT(*) as cantidad
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            GROUP BY REQUESTTYPE
            ORDER BY COUNT(*) DESC
        """)
        oracle_requesttype = oracle_cursor.fetchall()
        oracle_rt_dict = {row[0]: row[1] for row in oracle_requesttype}
        
        print("Oracle REQUESTTYPE:")
        for row in oracle_requesttype:
            print(f"   {row[0]}: {row[1]:,}")
        
        # S3 REQUESTTYPE
        try:
            result = duck_conn.sql('''
                SELECT REQUESTTYPE, COUNT(*) as cantidad
                FROM read_parquet("output/********/LOG_USR.parquet")
                GROUP BY REQUESTTYPE
                ORDER BY COUNT(*) DESC
            ''')
            s3_requesttype = result.fetchall()
            s3_rt_dict = {row[0]: row[1] for row in s3_requesttype}
            
            print("\nS3 REQUESTTYPE:")
            for row in s3_requesttype:
                print(f"   {row[0]}: {row[1]:,}")
            
            # Comparación REQUESTTYPE
            print("\nCOMPARACIÓN REQUESTTYPE:")
            all_types = set(oracle_rt_dict.keys()) | set(s3_rt_dict.keys())
            for req_type in sorted(all_types):
                oracle_count = oracle_rt_dict.get(req_type, 0)
                s3_count = s3_rt_dict.get(req_type, 0)
                diff = s3_count - oracle_count
                status = "✅" if abs(diff) <= 10 else "❌" if diff < -10 else "⚠️"
                print(f"   {req_type:<25} Oracle: {oracle_count:>6,} S3: {s3_count:>6,} Diff: {diff:>6,} {status}")
                
        except Exception as e:
            print(f"Error S3 REQUESTTYPE: {e}")
        
        # 9. RESUMEN DE VALIDACIÓN
        print("\n9. RESUMEN DE VALIDACIÓN DE TIPOS:")
        print("=" * 80)
        
        issues = []
        
        print("VALIDACIÓN POR COLUMNA:")
        print("1. USERHISTID: Patrones de prefijos (AU., UM., US.)")
        print("2. TIPODOCUMENTO: Debe ser 'DNI' para Perú")
        print("3. DOCUMENTO: Números de 8 dígitos (DNI peruano)")
        print("4. MSISDN: Números de teléfono con prefijo 51")
        print("5. BANKDOMAIN: Dominios bancarios válidos")
        print("6. USERID: Identificadores con prefijo US.")
        print("7. ACCOUNTID: Números de cuenta con prefijo 501")
        print("8. REQUESTTYPE: Tipos de transacción válidos")
        
        print(f"\n10. CONCLUSIÓN:")
        print("=" * 80)
        
        if not issues:
            print("🎉 VALIDACIÓN DE TIPOS: EXITOSA")
            print("✅ Los tipos de información son compatibles con Oracle")
        else:
            print(f"⚠️ VALIDACIÓN PARCIAL: {len(issues)} problemas encontrados")
            for i, issue in enumerate(issues, 1):
                print(f"   {i}. {issue}")
        
        oracle_cursor.close()
        oracle_conn.close()
        duck_conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
