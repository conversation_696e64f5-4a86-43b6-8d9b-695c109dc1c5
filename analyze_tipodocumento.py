#!/usr/bin/env python3
"""
Análisis de la columna TIPODOCUMENTO en LOG_USR
Comparación entre Oracle y S3 para fecha 2025-06-03
"""

import pandas as pd
import duckdb
from datetime import datetime
import os

def setup_oracle_connection():
    """Configurar conexión a Oracle"""
    # Configurar variables de entorno para Oracle
    oracle_user = os.getenv('ORACLE_USER', 'USR_DATALAKE')
    oracle_password = os.getenv('ORACLE_PASSWORD')
    oracle_dsn = os.getenv('ORACLE_DSN')
    
    if not all([oracle_password, oracle_dsn]):
        print("ERROR: Configurar variables ORACLE_PASSWORD y ORACLE_DSN")
        return None
    
    try:
        connection = cx_Oracle.connect(
            user=oracle_user,
            password=oracle_password,
            dsn=oracle_dsn
        )
        return connection
    except Exception as e:
        print(f"Error conectando a Oracle: {e}")
        return None

def analyze_oracle_tipodocumento():
    """Analizar TIPODOCUMENTO en Oracle para fecha específica"""
    conn = setup_oracle_connection()
    if not conn:
        return None
    
    try:
        query = """
        SELECT 
            TIPODOCUMENTO,
            COUNT(*) as CANTIDAD,
            MIN(DOCUMENTO) as MIN_DOCUMENTO,
            MAX(DOCUMENTO) as MAX_DOCUMENTO,
            COUNT(DISTINCT DOCUMENTO) as DOCUMENTOS_UNICOS
        FROM USR_DATALAKE.LOG_USR
        WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
        GROUP BY TIPODOCUMENTO
        ORDER BY CANTIDAD DESC
        """
        
        df_oracle = pd.read_sql(query, conn)
        print("=== ANÁLISIS ORACLE - TIPODOCUMENTO ===")
        print(f"Total registros por TIPODOCUMENTO:")
        print(df_oracle.to_string(index=False))
        print(f"\nTotal registros Oracle: {df_oracle['CANTIDAD'].sum()}")
        
        # Análisis adicional de valores únicos
        query_distinct = """
        SELECT DISTINCT TIPODOCUMENTO
        FROM USR_DATALAKE.LOG_USR
        WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
        ORDER BY TIPODOCUMENTO
        """
        
        df_distinct = pd.read_sql(query_distinct, conn)
        print(f"\nValores únicos TIPODOCUMENTO en Oracle:")
        for tipo in df_distinct['TIPODOCUMENTO']:
            print(f"  - '{tipo}'")
        
        return df_oracle
        
    except Exception as e:
        print(f"Error ejecutando query Oracle: {e}")
        return None
    finally:
        conn.close()

def analyze_s3_tipodocumento():
    """Analizar TIPODOCUMENTO en archivo local generado por pipeline"""
    try:
        # Configurar DuckDB
        conn = duckdb.connect()

        # Leer archivo LOG_USR local
        local_path = "S3_LOG_USER/output/20250603/LOG_USR.parquet"

        query = f"""
        SELECT
            TIPODOCUMENTO,
            COUNT(*) as CANTIDAD,
            MIN(DOCUMENTO) as MIN_DOCUMENTO,
            MAX(DOCUMENTO) as MAX_DOCUMENTO,
            COUNT(DISTINCT DOCUMENTO) as DOCUMENTOS_UNICOS
        FROM read_parquet('{local_path}')
        GROUP BY TIPODOCUMENTO
        ORDER BY CANTIDAD DESC
        """

        df_s3 = conn.execute(query).df()
        print("\n=== ANÁLISIS PIPELINE LOCAL - TIPODOCUMENTO ===")
        print(f"Total registros por TIPODOCUMENTO:")
        print(df_s3.to_string(index=False))
        print(f"\nTotal registros Pipeline: {df_s3['CANTIDAD'].sum()}")

        # Análisis de valores únicos
        query_distinct = f"""
        SELECT DISTINCT TIPODOCUMENTO
        FROM read_parquet('{local_path}')
        ORDER BY TIPODOCUMENTO
        """

        df_distinct = conn.execute(query_distinct).df()
        print(f"\nValores únicos TIPODOCUMENTO en Pipeline:")
        for tipo in df_distinct['TIPODOCUMENTO']:
            print(f"  - '{tipo}'")

        return df_s3

    except Exception as e:
        print(f"Error analizando archivo local: {e}")
        return None

def compare_tipodocumento(df_oracle, df_s3):
    """Comparar resultados Oracle vs S3"""
    if df_oracle is None or df_s3 is None:
        print("No se pueden comparar - faltan datos")
        return
    
    print("\n=== COMPARACIÓN ORACLE vs S3 ===")
    
    # Merge para comparar
    comparison = pd.merge(
        df_oracle, df_s3, 
        on='TIPODOCUMENTO', 
        how='outer', 
        suffixes=('_ORACLE', '_S3')
    ).fillna(0)
    
    print("Comparación detallada:")
    print(comparison.to_string(index=False))
    
    # Identificar diferencias
    print("\n=== DIFERENCIAS ENCONTRADAS ===")
    for _, row in comparison.iterrows():
        tipo = row['TIPODOCUMENTO']
        oracle_count = int(row['CANTIDAD_ORACLE'])
        s3_count = int(row['CANTIDAD_S3'])
        
        if oracle_count != s3_count:
            diff = s3_count - oracle_count
            print(f"TIPODOCUMENTO '{tipo}': Oracle={oracle_count}, S3={s3_count}, Diff={diff}")
    
    # Totales
    total_oracle = int(comparison['CANTIDAD_ORACLE'].sum())
    total_s3 = int(comparison['CANTIDAD_S3'].sum())
    print(f"\nTOTALES: Oracle={total_oracle}, S3={total_s3}, Diferencia={total_s3-total_oracle}")

def main():
    """Función principal"""
    print("Iniciando análisis TIPODOCUMENTO - LOG_USR")
    print("Fecha: 2025-06-03")
    print("="*50)

    # Por ahora solo analizar el archivo local del pipeline
    print("NOTA: Analizando solo archivo local del pipeline")
    print("Para comparar con Oracle, configurar variables ORACLE_PASSWORD y ORACLE_DSN")
    print()

    # Analizar archivo local
    df_s3 = analyze_s3_tipodocumento()

    if df_s3 is not None:
        print("\n=== RESUMEN ANÁLISIS ===")
        print(f"Archivo analizado: S3_LOG_USER/output/20250603/LOG_USR.parquet")
        print(f"Total registros: {df_s3['CANTIDAD'].sum()}")
        print(f"Tipos de documento únicos: {len(df_s3)}")

    # Si hay variables Oracle, intentar comparación
    if os.getenv('ORACLE_PASSWORD') and os.getenv('ORACLE_DSN'):
        print("\n" + "="*50)
        print("Configuración Oracle detectada - Iniciando comparación...")
        df_oracle = analyze_oracle_tipodocumento()
        compare_tipodocumento(df_oracle, df_s3)

if __name__ == "__main__":
    main()
