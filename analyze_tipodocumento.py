#!/usr/bin/env python3
"""
Aná<PERSON>is de la columna TIPODOCUMENTO en LOG_USR
Comparación entre Oracle y S3 para fecha 2025-06-03
"""

import pandas as pd
import duckdb
import boto3
from datetime import datetime
import cx_Oracle
import os

def setup_oracle_connection():
    """Configurar conexión a Oracle"""
    # Configurar variables de entorno para Oracle
    oracle_user = os.getenv('ORACLE_USER', 'USR_DATALAKE')
    oracle_password = os.getenv('ORACLE_PASSWORD')
    oracle_dsn = os.getenv('ORACLE_DSN')
    
    if not all([oracle_password, oracle_dsn]):
        print("ERROR: Configurar variables ORACLE_PASSWORD y ORACLE_DSN")
        return None
    
    try:
        connection = cx_Oracle.connect(
            user=oracle_user,
            password=oracle_password,
            dsn=oracle_dsn
        )
        return connection
    except Exception as e:
        print(f"Error conectando a Oracle: {e}")
        return None

def analyze_oracle_tipodocumento():
    """<PERSON><PERSON><PERSON> en Oracle para fecha específica"""
    conn = setup_oracle_connection()
    if not conn:
        return None
    
    try:
        query = """
        SELECT 
            TIPODOCUMENTO,
            COUNT(*) as CANTIDAD,
            MIN(DOCUMENTO) as MIN_DOCUMENTO,
            MAX(DOCUMENTO) as MAX_DOCUMENTO,
            COUNT(DISTINCT DOCUMENTO) as DOCUMENTOS_UNICOS
        FROM USR_DATALAKE.LOG_USR
        WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
        GROUP BY TIPODOCUMENTO
        ORDER BY CANTIDAD DESC
        """
        
        df_oracle = pd.read_sql(query, conn)
        print("=== ANÁLISIS ORACLE - TIPODOCUMENTO ===")
        print(f"Total registros por TIPODOCUMENTO:")
        print(df_oracle.to_string(index=False))
        print(f"\nTotal registros Oracle: {df_oracle['CANTIDAD'].sum()}")
        
        # Análisis adicional de valores únicos
        query_distinct = """
        SELECT DISTINCT TIPODOCUMENTO
        FROM USR_DATALAKE.LOG_USR
        WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
        ORDER BY TIPODOCUMENTO
        """
        
        df_distinct = pd.read_sql(query_distinct, conn)
        print(f"\nValores únicos TIPODOCUMENTO en Oracle:")
        for tipo in df_distinct['TIPODOCUMENTO']:
            print(f"  - '{tipo}'")
        
        return df_oracle
        
    except Exception as e:
        print(f"Error ejecutando query Oracle: {e}")
        return None
    finally:
        conn.close()

def analyze_s3_tipodocumento():
    """Analizar TIPODOCUMENTO en archivo S3"""
    try:
        # Configurar AWS
        session = boto3.Session()
        
        # Configurar DuckDB con S3
        conn = duckdb.connect()
        conn.execute("INSTALL httpfs;")
        conn.execute("LOAD httpfs;")
        
        # Configurar credenciales AWS en DuckDB
        credentials = session.get_credentials()
        conn.execute(f"""
        SET s3_region='us-east-1';
        SET s3_access_key_id='{credentials.access_key}';
        SET s3_secret_access_key='{credentials.secret_key}';
        """)
        
        if credentials.token:
            conn.execute(f"SET s3_session_token='{credentials.token}';")
        
        # Leer archivo LOG_USR de S3
        s3_path = "s3://usr-datalake-reports/LOG_USR/2025/06/03/"
        
        query = f"""
        SELECT 
            TIPODOCUMENTO,
            COUNT(*) as CANTIDAD,
            MIN(DOCUMENTO) as MIN_DOCUMENTO,
            MAX(DOCUMENTO) as MAX_DOCUMENTO,
            COUNT(DISTINCT DOCUMENTO) as DOCUMENTOS_UNICOS
        FROM read_parquet('{s3_path}*.parquet')
        GROUP BY TIPODOCUMENTO
        ORDER BY CANTIDAD DESC
        """
        
        df_s3 = conn.execute(query).df()
        print("\n=== ANÁLISIS S3 - TIPODOCUMENTO ===")
        print(f"Total registros por TIPODOCUMENTO:")
        print(df_s3.to_string(index=False))
        print(f"\nTotal registros S3: {df_s3['CANTIDAD'].sum()}")
        
        # Análisis de valores únicos
        query_distinct = f"""
        SELECT DISTINCT TIPODOCUMENTO
        FROM read_parquet('{s3_path}*.parquet')
        ORDER BY TIPODOCUMENTO
        """
        
        df_distinct = conn.execute(query_distinct).df()
        print(f"\nValores únicos TIPODOCUMENTO en S3:")
        for tipo in df_distinct['TIPODOCUMENTO']:
            print(f"  - '{tipo}'")
        
        return df_s3
        
    except Exception as e:
        print(f"Error analizando S3: {e}")
        return None

def compare_tipodocumento(df_oracle, df_s3):
    """Comparar resultados Oracle vs S3"""
    if df_oracle is None or df_s3 is None:
        print("No se pueden comparar - faltan datos")
        return
    
    print("\n=== COMPARACIÓN ORACLE vs S3 ===")
    
    # Merge para comparar
    comparison = pd.merge(
        df_oracle, df_s3, 
        on='TIPODOCUMENTO', 
        how='outer', 
        suffixes=('_ORACLE', '_S3')
    ).fillna(0)
    
    print("Comparación detallada:")
    print(comparison.to_string(index=False))
    
    # Identificar diferencias
    print("\n=== DIFERENCIAS ENCONTRADAS ===")
    for _, row in comparison.iterrows():
        tipo = row['TIPODOCUMENTO']
        oracle_count = int(row['CANTIDAD_ORACLE'])
        s3_count = int(row['CANTIDAD_S3'])
        
        if oracle_count != s3_count:
            diff = s3_count - oracle_count
            print(f"TIPODOCUMENTO '{tipo}': Oracle={oracle_count}, S3={s3_count}, Diff={diff}")
    
    # Totales
    total_oracle = int(comparison['CANTIDAD_ORACLE'].sum())
    total_s3 = int(comparison['CANTIDAD_S3'].sum())
    print(f"\nTOTALES: Oracle={total_oracle}, S3={total_s3}, Diferencia={total_s3-total_oracle}")

def main():
    """Función principal"""
    print("Iniciando análisis TIPODOCUMENTO - LOG_USR")
    print("Fecha: 2025-06-03")
    print("="*50)
    
    # Analizar Oracle
    df_oracle = analyze_oracle_tipodocumento()
    
    # Analizar S3
    df_s3 = analyze_s3_tipodocumento()
    
    # Comparar
    compare_tipodocumento(df_oracle, df_s3)

if __name__ == "__main__":
    main()
