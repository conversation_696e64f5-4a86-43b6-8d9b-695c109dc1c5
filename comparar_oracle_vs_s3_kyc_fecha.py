#!/usr/bin/env python3
import oracledb
import duckdb
import boto3
import sys

def consultar_oracle():
    try:
        print("=== CONSULTANDO ORACLE ===")
        # Configuración de conexión a Oracle
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='*************:1521/MMONEY'
        )
        
        cursor = connection.cursor()
        
        # Consulta exacta
        query = """
        SELECT COUNT(*)
        FROM PDP_PROD10_MAINDB.KYC_DETAILS
        WHERE TRUNC(CREATED_ON) <= TO_DATE('2025-06-03', 'YYYY-MM-DD')
        """
        
        print(f"Ejecutando consulta Oracle:")
        print(f"  {query.strip()}")
        
        cursor.execute(query)
        result_oracle = cursor.fetchone()[0]
        
        print(f"Resultado Oracle: {result_oracle:,}")
        
        cursor.close()
        connection.close()
        
        return result_oracle
        
    except Exception as e:
        print(f"Error en Oracle: {str(e)}")
        return None

def consultar_s3():
    try:
        print("\n=== CONSULTANDO S3 ===")
        
        # Configurar credenciales S3
        session = boto3.Session()
        credentials = session.get_credentials()
        
        # Crear conexión DuckDB
        conn = duckdb.connect()
        
        # Configurar S3
        conn.execute(f"""
            CREATE SECRET s3_secret (
                TYPE S3,
                KEY_ID '{credentials.access_key}',
                SECRET '{credentials.secret_key}',
                SESSION_TOKEN '{credentials.token}',
                REGION 'us-east-1'
            );
        """)
        
        # Ruta S3
        kyc_s3_path = "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/KYC_DETAILS_ORA/consolidado_puro.parquet"
        
        # Consulta equivalente en S3
        query = f"""
        SELECT COUNT(*)
        FROM read_parquet('{kyc_s3_path}')
        WHERE CAST(CREATED_ON AS DATE) <= CAST('2025-06-03' AS DATE)
        """
        
        print(f"Ejecutando consulta S3:")
        print(f"  {query}")
        
        result_s3 = conn.execute(query).fetchone()[0]
        
        print(f"Resultado S3: {result_s3:,}")
        
        conn.close()
        
        return result_s3
        
    except Exception as e:
        print(f"Error en S3: {str(e)}")
        return None

def main():
    print("=== COMPARACIÓN DIRECTA ORACLE vs S3 - KYC_DETAILS CON FECHA ===")
    
    # Consultar Oracle
    oracle_count = consultar_oracle()
    
    # Consultar S3
    s3_count = consultar_s3()
    
    # Comparar resultados
    print("\n=== COMPARACIÓN FINAL ===")
    print(f"{'FUENTE':<15} {'CANTIDAD':<15} {'DIFERENCIA':<15} {'% DIFF':<10}")
    print("=" * 60)
    
    if oracle_count is not None and s3_count is not None:
        diferencia = s3_count - oracle_count
        porcentaje = (diferencia / oracle_count) * 100 if oracle_count > 0 else 0
        
        print(f"{'Oracle':<15} {oracle_count:<15,} {'-':<15} {'-':<10}")
        print(f"{'S3':<15} {s3_count:<15,} {diferencia:<+15,} {porcentaje:<+10.3f}%")
        
        if abs(diferencia) == 0:
            print("\n✅ PERFECTO: Oracle y S3 tienen exactamente la misma cantidad")
        elif abs(diferencia) <= 100:
            print(f"\n⚠️  DIFERENCIA MENOR: {abs(diferencia)} registros ({abs(porcentaje):.3f}%)")
        else:
            print(f"\n❌ DIFERENCIA SIGNIFICATIVA: {abs(diferencia)} registros ({abs(porcentaje):.3f}%)")
            
        # Análisis adicional si hay diferencia
        if diferencia != 0:
            print(f"\n=== ANÁLISIS ADICIONAL ===")
            if diferencia > 0:
                print(f"S3 tiene {diferencia:,} registros MÁS que Oracle")
                print("Posibles causas: Datos duplicados en S3 o migración con registros adicionales")
            else:
                print(f"S3 tiene {abs(diferencia):,} registros MENOS que Oracle")
                print("Posibles causas: Migración incompleta o filtros en el proceso ETL")
    else:
        print("❌ No se pudieron obtener ambos resultados para comparar")

if __name__ == "__main__":
    main()
