#!/usr/bin/env python3
"""
Análisis de la columna ID_TYPE en las fuentes de datos
Para identificar por qué TIPODOCUMENTO contiene nombres de ciudades
"""

import pandas as pd
import duckdb
import boto3

def setup_duckdb_s3():
    """Configurar DuckDB con acceso a S3"""
    session = boto3.Session()
    conn = duckdb.connect()
    conn.execute("INSTALL httpfs;")
    conn.execute("LOAD httpfs;")
    
    credentials = session.get_credentials()
    conn.execute(f"""
    SET s3_region='us-east-1';
    SET s3_access_key_id='{credentials.access_key}';
    SET s3_secret_access_key='{credentials.secret_key}';
    """)
    
    if credentials.token:
        conn.execute(f"SET s3_session_token='{credentials.token}';")
    
    return conn

def analyze_user_profile_id_type():
    """Analizar ID_TYPE en USER_PROFILE"""
    try:
        conn = setup_duckdb_s3()
        
        s3_path = "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/USER_PROFILE_ORA/consolidado_puro.parquet"
        
        print("=== ANÁLISIS USER_PROFILE - ID_TYPE ===")
        
        # Verificar si existe la columna ID_TYPE
        try:
            schema_query = f"DESCRIBE SELECT * FROM read_parquet('{s3_path}') LIMIT 1"
            schema_result = conn.execute(schema_query).df()
            print("Columnas disponibles en USER_PROFILE:")
            for col in schema_result['column_name']:
                print(f"  - {col}")
            
            # Buscar columnas relacionadas con tipo de documento
            id_columns = [col for col in schema_result['column_name'] if 'ID' in col.upper() or 'TYPE' in col.upper() or 'DOC' in col.upper()]
            print(f"\nColumnas relacionadas con ID/TYPE/DOC: {id_columns}")
            
            # Si existe ID_TYPE, analizarla
            if 'ID_TYPE' in schema_result['column_name'].values:
                query = f"""
                SELECT 
                    ID_TYPE,
                    COUNT(*) as CANTIDAD
                FROM read_parquet('{s3_path}')
                WHERE ID_TYPE IS NOT NULL
                GROUP BY ID_TYPE
                ORDER BY CANTIDAD DESC
                LIMIT 20
                """
                
                df_result = conn.execute(query).df()
                print("\nValores únicos de ID_TYPE en USER_PROFILE:")
                print(df_result.to_string(index=False))
            else:
                print("\nColumna ID_TYPE no encontrada en USER_PROFILE")
                
        except Exception as e:
            print(f"Error analizando USER_PROFILE: {e}")
            
    except Exception as e:
        print(f"Error general en USER_PROFILE: {e}")

def analyze_user_data_trx_local():
    """Analizar el archivo USER_DATA_TRX local generado"""
    try:
        conn = duckdb.connect()
        
        local_path = "S3_LOG_USER/TEMP_LOGS_USUARIOS/20250603/USER_DATA_TRX.parquet"
        
        print("\n=== ANÁLISIS USER_DATA_TRX LOCAL - ID_TYPE ===")
        
        # Verificar esquema
        schema_query = f"DESCRIBE SELECT * FROM read_parquet('{local_path}') LIMIT 1"
        schema_result = conn.execute(schema_query).df()
        print("Columnas disponibles en USER_DATA_TRX:")
        for col in schema_result['column_name']:
            print(f"  - {col}")
        
        # Analizar ID_TYPE
        if 'ID_TYPE' in schema_result['column_name'].values:
            query = f"""
            SELECT 
                ID_TYPE,
                COUNT(*) as CANTIDAD
            FROM read_parquet('{local_path}')
            WHERE ID_TYPE IS NOT NULL AND ID_TYPE != ''
            GROUP BY ID_TYPE
            ORDER BY CANTIDAD DESC
            LIMIT 20
            """
            
            df_result = conn.execute(query).df()
            print("\nValores únicos de ID_TYPE en USER_DATA_TRX:")
            print(df_result.to_string(index=False))
            
            # Mostrar algunos ejemplos
            sample_query = f"""
            SELECT 
                ID_TYPE,
                ID_VALUE,
                PROFILE,
                ISSUER_CODE
            FROM read_parquet('{local_path}')
            WHERE ID_TYPE IS NOT NULL AND ID_TYPE != ''
            LIMIT 10
            """
            
            df_sample = conn.execute(sample_query).df()
            print("\nEjemplos de registros con ID_TYPE:")
            print(df_sample.to_string(index=False))
        else:
            print("\nColumna ID_TYPE no encontrada en USER_DATA_TRX")
            
    except Exception as e:
        print(f"Error analizando USER_DATA_TRX local: {e}")

def analyze_kyc_details():
    """Analizar KYC_DETAILS que podría tener información de documentos"""
    try:
        conn = setup_duckdb_s3()
        
        s3_path = "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/KYC_DETAILS_ORA/consolidado_puro.parquet"
        
        print("\n=== ANÁLISIS KYC_DETAILS ===")
        
        # Verificar esquema
        schema_query = f"DESCRIBE SELECT * FROM read_parquet('{s3_path}') LIMIT 1"
        schema_result = conn.execute(schema_query).df()
        print("Columnas disponibles en KYC_DETAILS:")
        for col in schema_result['column_name']:
            print(f"  - {col}")
        
        # Buscar columnas relacionadas con documentos
        doc_columns = [col for col in schema_result['column_name'] if any(keyword in col.upper() for keyword in ['ID', 'TYPE', 'DOC', 'DOCUMENT', 'DNI', 'CE'])]
        print(f"\nColumnas relacionadas con documentos: {doc_columns}")
        
        # Si hay columnas relevantes, analizarlas
        if doc_columns:
            for col in doc_columns[:3]:  # Analizar las primeras 3
                try:
                    query = f"""
                    SELECT 
                        {col},
                        COUNT(*) as CANTIDAD
                    FROM read_parquet('{s3_path}')
                    WHERE {col} IS NOT NULL
                    GROUP BY {col}
                    ORDER BY CANTIDAD DESC
                    LIMIT 10
                    """
                    
                    df_result = conn.execute(query).df()
                    print(f"\nValores únicos de {col}:")
                    print(df_result.to_string(index=False))
                except Exception as e:
                    print(f"Error analizando columna {col}: {e}")
                    
    except Exception as e:
        print(f"Error analizando KYC_DETAILS: {e}")

def main():
    """Función principal"""
    print("Iniciando análisis de fuentes ID_TYPE")
    print("="*60)
    
    # Analizar archivo local primero
    analyze_user_data_trx_local()
    
    # Analizar fuentes S3
    analyze_user_profile_id_type()
    analyze_kyc_details()
    
    print("\n" + "="*60)
    print("CONCLUSIÓN:")
    print("Verificar si ID_TYPE contiene realmente tipos de documento")
    print("o si hay un mapeo incorrecto en el pipeline")

if __name__ == "__main__":
    main()
