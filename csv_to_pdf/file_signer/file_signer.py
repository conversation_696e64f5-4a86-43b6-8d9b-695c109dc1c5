import os
import logging
from logging.handlers import TimedRotatingFileHandler
from datetime import datetime
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.x509 import load_pem_x509_certificate
from cryptography.hazmat.backends import default_backend

# Configuración del logger
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

# Obtener la fecha actual para el nombre del archivo de log
current_date = datetime.now().strftime("%Y-%m-%d")
log_file = os.path.join(log_dir, f'file_signer_{current_date}.log')

file_handler = TimedRotatingFileHandler(log_file, when="midnight", interval=1)
file_handler.suffix = "%Y-%m-%d"
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)

console_handler = logging.StreamHandler()
console_handler.setFormatter(formatter)

logger = logging.getLogger()
logger.setLevel(logging.INFO)
logger.addHandler(file_handler)
logger.addHandler(console_handler)

class FileSigner:
    def __init__(self, private_key_path: str):
        self.private_key = self._load_private_key(private_key_path)
        logging.info("FileSigner initialized with private key: %s", private_key_path)

    def _load_private_key(self, path: str):
        with open(path, "rb") as key_file:
            logging.info("Loading private key from: %s", path)
            return serialization.load_pem_private_key(key_file.read(), password=None)

    def _load_public_key(self, path: str):
        with open(path, "rb") as cert_file:
            cert_data = cert_file.read()
            logging.info("Loading public key from certificate: %s", path)
            cert = load_pem_x509_certificate(cert_data, default_backend())
            return cert.public_key()

    def sign_file(self, file_path: str):
        logging.info("Signing file: %s", file_path)
        with open(file_path, "rb") as f:
            file_data = f.read()

        signature = self.private_key.sign(
            file_data,
            padding.PSS(mgf=padding.MGF1(hashes.SHA256()), salt_length=padding.PSS.MAX_LENGTH),
            hashes.SHA256(),
        )

        signature_path = f"{file_path}.signature"
        with open(signature_path, "wb") as sig_file:
            sig_file.write(signature)
        
        logging.info("File signed. Signature saved to: %s", signature_path)
        return signature_path

    def verify_signature(self, file_path: str, signature_path: str, public_key_path: str) -> bool:
        logging.info("Verifying signature for file: %s with signature: %s", file_path, signature_path)
        with open(file_path, "rb") as f:
            file_data = f.read()
        with open(signature_path, "rb") as sig_file:
            signature = sig_file.read()

        public_key = self._load_public_key(public_key_path)

        try:
            public_key.verify(
                signature,
                file_data,
                padding.PSS(mgf=padding.MGF1(hashes.SHA256()), salt_length=padding.PSS.MAX_LENGTH),
                hashes.SHA256(),
            )
            logging.info("Signature verification succeeded for file: %s", file_path)
            return True
        except Exception as e:
            logging.error("Signature verification failed for file: %s. Error: %s", file_path, str(e))
            return False