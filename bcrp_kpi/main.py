import sys
import traceback
sys.path.insert(1, "libs")

import datetime
import os

from config import OUT_DIR
from constants import *
from entity.report import Report
from report.nodisp import generate_nodisp_report
from report.rend_alias import generate_rend_alias_report
from report.rend_trans import generate_rend_trans_report
from report.efect_transf__transf_reporte_res import generate_transf_reports
from report.efect_alias__consulta_busqueda import generate_aliases_reports

def main():
    DATE = datetime.datetime.now() - datetime.timedelta(days = 1)
    DATE = DATE.replace(hour = 0, minute = 0, second = 0, microsecond = 0)

    if len(sys.argv) > 1:
        try:
            DATE = datetime.datetime.strptime(sys.argv[1], "%Y-%m-%d")
        except Exception as e:
            try:
                DATE = datetime.datetime.strptime(sys.argv[1], "%Y/%m/%d")
            except Exception as e:
                traceback.print_exc()
                print(f"ERROR Parsing date: [{e}]")
                return

    print(f"Iniciando reportes para la fecha {DATE}")
    DATE_PATH = DATE + datetime.timedelta(days=1)        
    PERIOD = PERIODO[0]
    REPORT_PATH = rf"{OUT_DIR}"
    if not os.path.exists(REPORT_PATH):
        os.makedirs(REPORT_PATH)

    print(f"Inicio Reporte {EFECT_ALIAS_NAME}")
    efect_alias, consulta_busqueda = generate_aliases_reports(DATE, PERIOD)
    report_efect_alias = Report(
        EFECT_ALIAS_HEADER,
        EFECT_ALIAS_NAME,
        DATE,
        PERIOD,
    )
    report_efect_alias.data = efect_alias
    report_efect_alias.write_report(REPORT_PATH, DATE_PATH)

    print(f"Inicio Reporte {CONSULTA_BUSQUEDA_NAME}")
    report_consulta_busqueda = Report(
        CONSULTA_BUSQUEDA_HEADER,
        CONSULTA_BUSQUEDA_NAME,
        DATE,
        PERIOD,
    )
    report_consulta_busqueda.data = consulta_busqueda
    report_consulta_busqueda.write_report(REPORT_PATH, DATE_PATH)

    print(f"Inicio Reporte {EFECT_TRANSF_NAME}")
    efect_transf, transf_reporte_res = generate_transf_reports(DATE, PERIOD)
    report_efect_transf = Report(
        EFECT_TRANSF_HEADER,
        EFECT_TRANSF_NAME,
        DATE,
        PERIOD,
    )
    report_efect_transf.data = [efect_transf]
    report_efect_transf.write_report(REPORT_PATH, DATE_PATH)

    print(f"Inicio Reporte {TRANSF_REPORTE_RES_NAME}")
    report_transf_reporte_res = Report(
        [],
        TRANSF_REPORTE_RES_NAME,
        DATE,
        PERIOD,
    )
    report_transf_reporte_res.write_custom_report(REPORT_PATH, transf_reporte_res, DATE_PATH)

    # Rendimiento
    print(f"Inicio Reporte {REND_ALIAS_NAME}")
    report_rend_alias = Report(
        REND_ALIAS_HEADER,
        REND_ALIAS_NAME,
        DATE,
        PERIOD,
    )
    report_rend_alias.data = generate_rend_alias_report(DATE, PERIOD)
    report_rend_alias.write_report(REPORT_PATH, DATE_PATH)

    print(f"Inicio Reporte {REND_TRANS_NAME}")
    report_rend_trans = Report(
        REND_TRANS_HEADER,
        REND_TRANS_NAME,
        DATE,
        PERIOD,
    )
    report_rend_trans.data = generate_rend_trans_report(DATE, PERIOD)
    report_rend_trans.write_report(REPORT_PATH, DATE_PATH)

    print(f"Inicio Reporte {NODISP_NAME}")
    report_nodisp = Report(
        NODISP_HEADER,
        NODISP_NAME,
        DATE,
        PERIOD,
    )
    report_nodisp.data = generate_nodisp_report(DATE, PERIOD)
    report_nodisp.write_report(REPORT_PATH, DATE_PATH)

    print(f"Fin Reportes {DATE}")

if __name__ == "__main__":
    main()
