ID_ENTIDAD = "0904"
NOMBRE_ENTIDAD = "EEDE_BIM"
ROL_ENTIDAD = "Entidad Ordenante"

PERIODO = [
    ("06:00:00", "22:00:00"),
]

RIELES_DE_PAGO = {"NIUBIZ": "0031"}
ENTIDADES_BENEFICIARIAS = {"YAPE": "0025", "PLIN": "0029"}

REND_ALIAS_HEADER = [
    "Id Entidad",
    "Nombre de la Entidad Regulada",
    "Rol de la Entidad Regulada",
    "Calidad del Servicio",
    "Año",
    "Mes",
    "Dia",
    "KPI",
    "Func. Especifica",
    "Tipo KPI",
    "Cant. de Consultas Totales del Día (Éxitos )",
    "Cantidad de Consultas Totales del Día (Éxitos) <= t  segundos",
    "Cantidad de Consultas Totales del Día (Éxitos) >= tmax segundos",
    "KPI Resultado",
    "Comentarios",
    "id Gestor",
]

REND_ALIAS_NAME = "EEDE_BIM_REND_ALIAS_{}.csv"

REND_TRANS_HEADER = [
    "Id Entidad",
    "Nombre de la Entidad Regulada",
    "Rol de la Entidad Regulada",
    "Calidad del Servicio",
    "Año",
    "Mes",
    "Dia",
    "KPI",
    "Func. Especifica",
    "Tipo KPI",
    "Cant. de Transferencias Totales del Día (Éxitos )",
    "Cantidad de Transferencias Totales del Día (Éxitos) <= t  segundos",
    "Cantidad de Transferencias Totales del Día (Éxitos) >= tmax segundos",
    "KPI Resultado",
    "Comentarios",
    "Id Procesador",
]

REND_TRANS_NAME = "EEDE_BIM_REND_TRANS_{}.csv"

NODISP_HEADER = [
    "Id Entidad",
    "Nombre de la Entidad Regulada",
    "Rol de la Entidad Regulada",
    "Calidad del Servicio",
    "KPI",
    "Min. Totales Dia Mes",
    "Min. Totales Indisponibilidad",
    "Min. Totales Mantenimientos",
    "KPI Resultado",
    "Comentarios",
    "Año",
    "Mes",
    "Dia",
]

NODISP_NAME = "EEDE_BIM_NODISP_{}.csv"

EFECT_TRANSF_HEADER = [
    "Id Entidad",
    "Nombre de la Entidad Regulada",
    "Rol de la Entidad Regulada",
    "Calidad del Servicio",
    "Año",
    "Me",
    "Dia",
    "KPI",
    "Func. Especifica",
    "# Int. Totales Transferencias",
    "# Fallas Entidad Ordenante",
    "# Fallas Niubiz/Visa",
    "# Fallas CCE",
    "# Fallas Izipay",
    "KPI Resultado",
    "Comentarios",
]

EFECT_TRANSF_NAME = "EEDE_BIM_EFECT_TRANSF_{}.csv"

TRANSF_REPORTE_RES_NAME = "EEDE_BIM_TRANSF_REPORTE_RES_{}.csv"

EFECT_ALIAS_HEADER = [
    "Id Entidad",
    "Nombre de la Entidad Regulada",
    "Rol de la Entidad Regulada",
    "Calidad del Servicio" "Año",
    "Mes",
    "Dia",
    "KPI",
    "Func. Especifica",
    "# Int. Totales Consulta",
    "# Fallas Entidad Ordenante",
    "# Fallas Yellow Pepper",
    "# Fallas CCE",
    "KPI Resultado",
    "Comentarios",
]

EFECT_ALIAS_NAME = "EEDE_BIM_EFECT_ALIAS_{}.csv"

CONSULTA_BUSQUEDA_HEADER = [
    "ID Gestor De Directorio",
    "Funcionalidad Específica",
    "Cantidad de Consultas Totales",
    "Cantidad de Consultas Exitosas",
    "Cantidad de Consultas Erradas",
    "Cantidad de Consultas QR",
]
CONSULTA_BUSQUEDA_NAME = "EEDE_BIM_CONSULTA_BUSQUEDA_{}.csv"
