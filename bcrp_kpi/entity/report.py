import datetime
from sends3 import upload_s3


class Report:
    def __init__(
        self, headers: list[str], name: str, date: datetime.datetime, periodo: tuple
    ) -> None:
        self.headers: list[str] = headers
        self.name: str = name
        self.date = date
        self.start_time = periodo[0]
        self.end_time = periodo[1]
        self.data: list[list[str]] = []


    def write_report(self, path, date_param) -> None:
        output_list = []
        output_list.append(self.headers)
        output_list.extend(self.data)

        output = ""
        for line in output_list:
            output += ";".join(line)
            output += "\n"

        date = self.date.strftime("%d%m%Y")
        file_extension = self.name.split('.')[-1].lower()
        if file_extension == "csv":
            folder_type = "csv/"
        elif file_extension in ["xls", "xlsx"]:
            folder_type = "excel/"
        file_path = f"{path}{folder_type}{self.name.format(date)}"
        with open(file_path, "w", encoding="UTF-8") as f:
            f.write(output)
        #file_extension = file_path.split('.')[-1].lower()
        base_name = self.name.split('_{')[0]
        print(f"Base name: {base_name}")  
        s3_path= f"BCRP/{date_param}/{base_name}/"
        upload_s3(file_path, self.name.format(date), s3_path)

    def write_custom_report(self, path, info, date_param) -> None:
        output = ""
        for line in info:
            output += ";".join(line)
            output += "\n"

        date = self.date.strftime("%d%m%Y")
        file_extension = self.name.split('.')[-1].lower()
        #file_extension = ""
        if file_extension == "csv":
            folder_type = "csv/"
        elif file_extension in ["xls", "xlsx"]:
            folder_type = "excel/"
        file_path = f"{path}{folder_type}{self.name.format(date)}"
        with open(file_path, "w", encoding="UTF-8") as f:
            f.write(output)
            
        base_name = self.name.split('_{')[0]
        s3_path = f"BCRP/{date_param}/{base_name}/"
        upload_s3(file_path, self.name.format(date), s3_path)
