import pymysql as pymysql
import pymysql.cursors
import config as cfg


class MySQLData:
    """Database connection class."""

    def __init__(self):
        self.db_host = cfg.DB_HOST
        self.db_username = cfg.DB_USERNAME
        self.db_password = cfg.DB_PASSWORD
        self.db_port = cfg.DB_PORT
        self.db_name = cfg.DB_NAME
        self.conn = None

    def open_connection(self):
        """Connect to MySQL Database."""
        try:
            if self.conn is None:
                self.conn = pymysql.connect(
                    host=self.db_host,
                    db=self.db_name,
                    port=self.db_port,
                    user=self.db_username,
                    password=self.db_password,
                    charset="utf8mb4",
                    cursorclass=pymysql.cursors.DictCursor,
                    connect_timeout=30,
                )
        except pymysql.MySQLError as e:
            print("ERROR pymysql", e)
        except Exception as e:
            print("ERROR otro", e)
        finally:
            print("Connection opened successfully.")

    def select_query(self, query, param):
        """Execute SQL query."""
        try:
            formatted_query = query % param
            print("Formatted Query: ", formatted_query)

            self.open_connection()
            with self.conn.cursor() as cur:
                records = []
                cur.execute(query, param)
                for row in cur:
                    print(row)
                    records.append(row)
                return records

        except pymysql.MySQLError as e:
            print("ERROR:", e)

        except Exception as e:
            print("ERROR:", e)

        finally:
            if self.conn:
                self.conn.close()
                self.conn = None
                print("Database connection closed.")

    def insert_query(self, query, param):
        """Execute SQL query."""
        try:
            self.open_connection()
            with self.conn.cursor() as cur:
                print(cur.mogrify(query, param))
                result = cur.execute(query, param)
                self.conn.commit()
                affected = cur.rowcount
                cur.close()
                return affected

        except pymysql.MySQLError as e:
            print("ERROR:", e)

            return e.args[0], str(e)
        finally:
            if self.conn:
                self.conn.close()
                self.conn = None
                print("Database connection closed.")
