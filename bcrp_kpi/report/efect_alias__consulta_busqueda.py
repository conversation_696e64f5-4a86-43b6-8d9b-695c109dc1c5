from entity.db import MySQLData
from constants import ID_ENTIDAD, NOMBRE_ENTIDAD, ROL_ENTIDAD

QUERY = """
SELECT
	YEAR(created_at) anio, 
	MONTH(created_at) mes, 
	api_name, 
	status, 
	count(id_consulta) total,
	SUM(COUNT(id_consulta)) OVER (PARTITION BY api_name) AS total_suma
FROM
	consulta_api_yellowpepper 
WHERE 
    (created_at) >= %(start_date)s AND (created_at) <= %(end_date)s
GROUP BY 
	MONTH(created_at), api_name, status 
ORDER BY 
	anio, mes;
"""

mysql = MySQLData()


def generate_aliases_reports(date, period):
    start_time = date.strftime(f"%Y-%m-%d {period[0]}")
    end_time = date.strftime(f"%Y-%m-%d {period[1]}")
    params = {
        "start_date": start_time,
        "end_date": end_time,
    }
    print(QUERY)
    print(params)
    data = mysql.select_query(QUERY, params)
    print(data)
    result = {
        "inquiry": {
            "success": 0,
            "failed": 0,
        },
        "resolve": {
            "success": 0,
            "failed": 0,
        },
    }

    for entry in data:
        api = entry.get("api_name")
        total = entry.get("total")
        status = entry.get("status")

        key = "success" if status == "200" else "failed"
        result[api][key] += total

    efect_alias = []
    for key, value in result.items():
        print("Generated Append")
        
        success_failed_sum = value["success"] + value["failed"]
        if success_failed_sum == 0:
            porcentaje_fallido = "0%"
        else:
            porcentaje_fallido = f"{round(100 * value['failed'] / success_failed_sum, 2)}%"
        
        efect_alias.append(
            [
                ID_ENTIDAD,
                NOMBRE_ENTIDAD,
                ROL_ENTIDAD,
                "Efectividad",
                str(date.year),
                str(date.month),
                str(date.day),
                "Busqueda Alias",
                f"Alias {key.title()}",
                str(value["success"] + value["failed"]),
                "",
                str(value["failed"]),
                "",
                #f"""{round(100 * value["failed"] / (value["success"] + value["failed"]), 2)}%"""
                porcentaje_fallido,
                "",
            ]
        )
    consulta_busqueda = []
    for key, value in result.items():
        consulta_busqueda.append(
            [
                "Yellow Pepper",
                f"Alias {key.title()}",
                str(value["success"] + value["failed"]),
                str(value["success"]),
                str(value["failed"]),
                "",
            ]
        )
    return efect_alias, consulta_busqueda
