import datetime

from constants import ID_ENTIDAD, NOMBRE_ENTIDAD, ROL_ENTIDAD
from globals import *
from utils import transform_date_period


def generate_nodisp_report(date, period) -> list[list[str]]:
	output: list[list[str]] = []
	start_datetime, end_datetime = transform_date_period(date, period)
	LAMBDA_NAME = "prod_alias-resolve-v5_directory"

	data = CLOUDWATCH_CLIENT.get_metric_data(
		MetricDataQueries=[
			{
				"Id": "errors",
				"MetricStat": {
					"Metric": {
						"Namespace": "AWS/Lambda",
						"MetricName": "Errors",
						"Dimensions": [
							{
								"Name": "FunctionName",
								"Value": LAMBDA_NAME
							},
						]
					},
					"Period": 60,
					"Stat": "Sum",
				},
				"ReturnData": True,
			},
			{
				"Id": "invocations",
				"MetricStat": {
					"Metric": {
						"Namespace": "AWS/Lambda",
						"MetricName": "Invocations",
						"Dimensions": [
							{
								"Name": "FunctionName",
								"Value": LAMBDA_NAME
							},
						]
					},
					"Period": 60,
					"Stat": "Sum",
				},
				"ReturnData": True,
			},
			{
				"Id": "availability",
				"Expression": "100 - 100 * errors / MAX([errors, invocations])",
				"ReturnData": True,
			},
		],
		StartTime=start_datetime,
		EndTime=end_datetime,
		#MaxDatapoints=123,
		LabelOptions={
			"Timezone": "-0500"
		}
	)

	x_values = data["MetricDataResults"][2]["Timestamps"]
	y_values = data["MetricDataResults"][2]["Values"]

	rango_medicion = int((end_datetime - start_datetime).total_seconds() / 60.0)

	min_indisponibilidad = 0
	for i in y_values:
		if i < 85:
			min_indisponibilidad += 1

	kpi_resultado = min_indisponibilidad / rango_medicion * 100

	output.append([
		ID_ENTIDAD,
		NOMBRE_ENTIDAD,
		ROL_ENTIDAD,
		"No Disponibilidad",
		"Busqueda Alias",
		str(rango_medicion),
		str(min_indisponibilidad),
		str(0),
		f"{kpi_resultado:.2f}%",
		"",
		str(date.year),
		str(date.month),
		str(date.day),
	])
	

	return output