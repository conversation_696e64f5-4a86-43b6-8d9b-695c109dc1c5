import sys
import os
import pytest
import sqlite3
import tempfile
import shutil
import logging

# Agrega el directorio raíz del proyecto al PYTHONPATH
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from src.main import main

def test_generate_reports(monkeypatch, caplog):
    # Simula la base de datos en memoria
    conn = sqlite3.connect(':memory:')
    cursor = conn.cursor()
    
    # Crea tablas y datos de prueba
    cursor.execute('''CREATE TABLE example (id INTEGER PRIMARY KEY, name TEXT)''')
    cursor.execute('''INSERT INTO example (name) VALUES ('Test Data')''')
    conn.commit()
    
    # Simula las funciones que generan los reportes
    def mock_generate_aliases_reports(date, period):
        return [('Test Alias',)], [('Test Consulta',)]
    
    def mock_generate_transf_reports(date, period):
        return 'Test Efect Transf', 'Test Transf Reporte Res'
    
    def mock_generate_rend_alias_report(date, period):
        return 'Test Rend Alias'
    
    def mock_generate_rend_trans_report(date, period):
        return 'Test Rend Trans'
    
    def mock_generate_nodisp_report(date, period):
        return 'Test Nodisp'
    
    # Usa monkeypatch para reemplazar las funciones originales con las simuladas
    monkeypatch.setattr('src.report.efect_alias__consulta_busqueda.generate_aliases_reports', mock_generate_aliases_reports)
    monkeypatch.setattr('src.report.efect_transf__transf_reporte_res.generate_transf_reports', mock_generate_transf_reports)
    monkeypatch.setattr('src.report.rend_alias.generate_rend_alias_report', mock_generate_rend_alias_report)
    monkeypatch.setattr('src.report.rend_trans.generate_rend_trans_report', mock_generate_rend_trans_report)
    monkeypatch.setattr('src.report.nodisp.generate_nodisp_report', mock_generate_nodisp_report)
    
    # Crea un directorio temporal para los reportes
    temp_dir = tempfile.mkdtemp()
    monkeypatch.setattr('src.main.OUT_DIR', temp_dir)
    
    # Ejecuta la función principal
    with caplog.at_level(logging.INFO):
        main()
    
    # Verifica que los reportes se generaron correctamente
    expected_reports = [
        'efect_alias_report.csv',
        'consulta_busqueda_report.csv',
        'efect_transf_report.csv',
        'transf_reporte_res_report.csv',
        'rend_alias_report.csv',
        'rend_trans_report.csv',
        'nodisp_report.csv'
    ]
    
    for report in expected_reports:
        assert os.path.exists(os.path.join(temp_dir, report))
    
    # Verifica los logs
    assert "Iniciando reportes para la fecha" in caplog.text
    assert "Fin Reportes" in caplog.text
    
    # Limpia el directorio temporal
    shutil.rmtree(temp_dir)

if __name__ == "__main__":
    pytest.main()