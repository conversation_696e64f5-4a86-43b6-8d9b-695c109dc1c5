# Reporte de Log de Usuarios

Este reporte genera logs de actividad de usuarios, incluyendo:
- Modificaciones de usuarios
- Cambios de autenticación
- Activaciones de usuarios
- Cierres de cuentas

## Tablas de origen

El reporte utiliza las siguientes tablas de S3:

1. **USER_PROFILE**: Perfiles de usuarios
   - Ruta: `s3://prd-datalake-bronze-zone-************/PDP_PROD10_REPORTING_MOBIQUITY/USER_PROFILE/`

2. **KYC_DETAILS**: Detalles de KYC (Know Your Customer)
   - Ruta: `s3://prd-datalake-bronze-zone-************/PDP_PROD10_REPORTING_MOBIQUITY/KYC_DETAILS/`

3. **ISSUER_DETAILS**: Detalles de emisores
   - Ruta: `s3://prd-datalake-bronze-zone-************/PDP_PROD10_REPORTING_MOBIQUITY/ISSUER_DETAILS/`

4. **MTX_CATEGORIES**: Categorías de transacciones
   - Ruta: `s3://prd-datalake-bronze-zone-************/PDP_PROD10_REPORTING_MOBIQUITY/MTX_CATEGORIES/`

5. **CHANNEL_GRADES**: Grados de canales
   - Ruta: `s3://prd-datalake-bronze-zone-************/PDP_PROD10_REPORTING_MOBIQUITY/CHANNEL_GRADES/`

6. **USER_MODIFICATION_HISTORY**: Historial de modificaciones de usuarios
   - Ruta: `s3://prd-datalake-bronze-zone-************/PDP_PROD10_REPORTING_MOBIQUITY/USER_MODIFICATION_HISTORY/`

7. **USER_AUTH_CHANGE_HISTORY**: Historial de cambios de autenticación
   - Ruta: `s3://prd-datalake-bronze-zone-************/PDP_PROD10_REPORTING_MOBIQUITY/USER_AUTH_CHANGE_HISTORY/`

8. **USER_IDENTIFIER**: Identificadores de usuarios
   - Ruta: `s3://prd-datalake-bronze-zone-************/PDP_PROD10_REPORTING_MOBIQUITY/USER_IDENTIFIER/`

9. **MTX_WALLET**: Billeteras de transacciones
   - Ruta: `s3://prd-datalake-bronze-zone-************/PDP_PROD10_REPORTING_MOBIQUITY/MTX_WALLET/`

10. **USER_ACCOUNT_HISTORY**: Historial de cuentas de usuarios
    - Ruta: `s3://prd-datalake-bronze-zone-************/APP_BIM_PROD_1/USER_ACCOUNT_HISTORY/`

## Ejecución

Para ejecutar el reporte:

```bash
python run_reporte.py REPORTE_LOG_USUARIO
```

## Configuración

La configuración del reporte se encuentra en `CONFIGURACIONES/REPORTE_LOG_USUARIO/config.ini`.

### Opciones de configuración:

- **output_dir**: Directorio donde se guardarán los archivos de salida
- **log_dir**: Directorio donde se guardarán los logs
- **rango**: Si es `true`, se utilizará un rango de fechas fijo; si es `false`, se utilizará un número de días hacia atrás
- **dias_atras**: Número de días hacia atrás para procesar (si rango=false)
- **rango_inicio**: Fecha de inicio del rango (si rango=true)
- **rango_fin**: Fecha de fin del rango (si rango=true)

## Salida

El reporte genera archivos Parquet con la siguiente estructura:

- `REPORTE_LOG_USUARIOS/{año}/{mes}/{día}/REPORTE_LOG_USUARIO_{fecha}_{hora}.parquet`

## Campos del reporte

El reporte incluye los siguientes campos principales:

- **userHistId**: Identificador único del historial de usuario
- **createdOn**: Fecha y hora de la acción
- **TipoDocumento**: Tipo de documento del usuario
- **Documento**: Número de documento del usuario
- **Msisdn**: Número de teléfono móvil
- **BankDomain**: Banco o dominio
- **created_by**: Usuario que realizó la acción
- **userId**: Identificador del usuario
- **accountType**: Tipo de cuenta
- **accountId**: Identificador de la cuenta
- **Nombre**: Nombre del usuario
- **Apellido**: Apellido del usuario
- **perfilA**: Perfil del usuario
- **requestType**: Tipo de solicitud (ActivateUser, ClosedAccount, etc.)
