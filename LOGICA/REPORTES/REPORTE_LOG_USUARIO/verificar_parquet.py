#!/usr/bin/env python3
import pandas as pd
import os
import json

# Ruta al archivo Parquet procesado
base_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))
parquet_file = os.path.join(base_dir, "REPORTE_LOG_USUARIOS/2025/05/05/REPORTE_LOG_USUARIO_2025-05-05_075902.parquet")

# Verificar que el archivo existe
if not os.path.exists(parquet_file):
    print(f"El archivo {parquet_file} no existe.")
    exit(1)

# Leer el archivo Parquet
print(f"Leyendo archivo Parquet: {parquet_file}")
df = pd.read_parquet(parquet_file)

# Verificar el número de registros
print(f"Número de registros: {len(df)}")

# Contar registros por userHistId
print("\nConteo de registros por userHistId:")
user_hist_counts = df['userHistId'].value_counts()
for user_hist_id, count in user_hist_counts.items():
    print(f"  {user_hist_id}: {count}")

# Verificar el formato de los campos JSON
print("\nVerificando el formato de los campos JSON:")
sample_df = df[df['oldData'].notnull() | df['newData'].notnull()].head(3)

for index, row in sample_df.iterrows():
    print(f"Registro {index}:")
    
    # Mostrar oldData
    if pd.notnull(row['oldData']):
        print(f"  oldData: {row['oldData'][:100]}...")
        
        # Verificar si está escapado
        if row['oldData'].startswith('"') and row['oldData'].endswith('"'):
            print(f"  oldData está escapado: Sí")
        else:
            print(f"  oldData está escapado: No")
        
        # Verificar si es un JSON válido
        try:
            json_obj = json.loads(row['oldData'])
            print(f"  oldData es JSON válido: Sí")
            
            # Mostrar la estructura del JSON
            print(f"  Estructura de oldData: {json.dumps(json_obj, indent=2)[:200]}...")
        except Exception as e:
            print(f"  oldData es JSON válido: No - {str(e)}")
    
    # Mostrar newData
    if pd.notnull(row['newData']):
        print(f"  newData: {row['newData'][:100]}...")
        
        # Verificar si está escapado
        if row['newData'].startswith('"') and row['newData'].endswith('"'):
            print(f"  newData está escapado: Sí")
        else:
            print(f"  newData está escapado: No")
        
        # Verificar si es un JSON válido
        try:
            json_obj = json.loads(row['newData'])
            print(f"  newData es JSON válido: Sí")
            
            # Mostrar la estructura del JSON
            print(f"  Estructura de newData: {json.dumps(json_obj, indent=2)[:200]}...")
        except Exception as e:
            print(f"  newData es JSON válido: No - {str(e)}")

print("\nVerificación completada.")
