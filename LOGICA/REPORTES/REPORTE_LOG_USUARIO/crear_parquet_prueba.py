#!/usr/bin/env python3
import pandas as pd
import os
import json

# Crear un directorio para el archivo Parquet
output_dir = "../../REPORTE_LOG_USUARIOS/2025/05/05"
os.makedirs(output_dir, exist_ok=True)

# Crear datos de prueba
data = {
    'userHistId': [
        'AU.SU13551639', 'AU.SU13551639', 'AU.SU13551639',
        'UM.4402', 'UM.4404', 'UM.4405', 'UM.5398', 'UM.5247',
        'UM.1001', 'UM.1002', 'UM.1003', 'UM.1004', 'UM.1005'
    ],
    'createdOn': ['2025-05-05'] * 13,
    'userId': ['user1', 'user2', 'user3', 'user4', 'user5', 'user6', 'user7', 'user8', 'user9', 'user10', 'user11', 'user12', 'user13'],
    'accountId': ['acc1', 'acc2', 'acc3', 'acc4', 'acc5', 'acc6', 'acc7', 'acc8', 'acc9', 'acc10', 'acc11', 'acc12', 'acc13'],
    'Msisdn': ['*********'] * 13,
    'MsisdnB': [None] * 13,
    'created_by': ['system'] * 13,
    'requestType': ['Auth Change', 'User Modification', 'Account Change', 'User Modification', 'User Modification', 'User Modification', 'User Modification', 'User Modification', 'User Modification', 'User Modification', 'User Modification', 'User Modification', 'User Modification'],
    'oldData': [
        None,
        '"{\"profileDetails\":[{\"identifierValue\":\"***********\",\"identifierType\":\"MSISDN\",\"remarks\":null}]}"',
        None,
        '"{\"notificationEndpointRequests\":[{\"endpointType\":\"EMAIL\",\"endpointValue\":\"<EMAIL>\"}]}"',
        '"{\"paymentHandles\":[{\"paymentType\":\"CARD\",\"cardNumber\":\"*********0123456\"}]}"',
        '"{\"kycs\":[{\"documentType\":\"DNI\",\"documentNumber\":\"********\"}]}"',
        '"{\"profileDetails\":[{\"identifierValue\":\"***********\",\"identifierType\":\"MSISDN\",\"remarks\":null}]}"',
        '"{\"profileDetails\":[{\"identifierValue\":\"***********\",\"identifierType\":\"MSISDN\",\"remarks\":null}]}"',
        None,
        None,
        None,
        None,
        None
    ],
    'newData': [
        None,
        '"{\"profileDetails\":[{\"identifierValue\":\"***********\",\"identifierType\":\"MSISDN\",\"remarks\":null}]}"',
        None,
        '"{\"notificationEndpointRequests\":[{\"endpointType\":\"EMAIL\",\"endpointValue\":\"<EMAIL>\"}]}"',
        '"{\"paymentHandles\":[{\"paymentType\":\"CARD\",\"cardNumber\":\"****************\"}]}"',
        '"{\"kycs\":[{\"documentType\":\"DNI\",\"documentNumber\":\"********\"}]}"',
        '"{\"profileDetails\":[{\"identifierValue\":\"***********\",\"identifierType\":\"MSISDN\",\"remarks\":null}]}"',
        '"{\"profileDetails\":[{\"identifierValue\":\"***********\",\"identifierType\":\"MSISDN\",\"remarks\":null}]}"',
        None,
        None,
        None,
        None,
        None
    ],
    'userIdOld': [None] * 13,
    'accountIdOld': [None] * 13
}

# Crear DataFrame
df = pd.DataFrame(data)

# Guardar como Parquet
output_file = os.path.join(output_dir, "REPORTE_LOG_USUARIO_2025-05-05_075902.parquet")
df.to_parquet(output_file, index=False)

print(f"Archivo Parquet de prueba creado: {output_file}")
print(f"Número de registros: {len(df)}")

# Mostrar algunos registros
print("\nEjemplos de registros:")
for idx, row in df.head(3).iterrows():
    print(f"Registro {idx}:")
    for col in df.columns:
        if pd.notnull(row[col]):
            if col in ['oldData', 'newData']:
                print(f"  {col}: {row[col][:50]}...")
            else:
                print(f"  {col}: {row[col]}")
