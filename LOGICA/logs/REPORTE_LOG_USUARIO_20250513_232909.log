2025-05-13 23:29:09,732 - INFO - <PERSON><PERSON>and<PERSON> datos para REPORTE_LOG_USUARIO desde 2025-05-01 hasta 2025-05-06
2025-05-13 23:29:09,732 - INFO - Procesando fecha: 2025-05-01
2025-05-13 23:29:09,758 - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-05-13 23:29:09,804 - INFO - Ejecutando query: log_usuarios_all
2025-05-13 23:30:03,656 - INFO - Se encontraron 17 registros para procesar
2025-05-13 23:30:03,656 - INFO - Eliminando carpeta existente para evitar duplicados: REPORTE_LOG_USUARIOS/2025/05/01
2025-05-13 23:30:03,663 - INFO - Resultados exportados a REPORTE_LOG_USUARIOS/2025/05/01/REPORTE_LOG_USUARIO_2025-05-01_232909.parquet
2025-05-13 23:30:03,685 - INFO - Muestra de datos procesados:
  userHistId   createdOn TipoDocumento Documento       Msisdn  MsisdnB    BankDomain        created_by         userId   accountType      accountId  ... PerfilCuentaA perfilCuentaB  TipoDocumentoA  TipoDocumentoB  DocumentoB  NumDocumentoB        requestType                                            oldData                                            newData  userIdOld  accountIdOld
0       UM.1  2025-05-01           DNI  ********  ***********     <NA>  FCOMPARTAMOS  ****************  *************  MOBILE_MONEY  *************  ...          <NA>          <NA>             DNI            <NA>    ********           <NA>  User Modification  "{\"notificationEndpointRequests\":[{\"notific...  "{\"notificationEndpointRequests\":[{\"notific...       <NA>          <NA>
1       UM.2  2025-05-01          None      None  ***********     <NA>  FCOMPARTAMOS  ****************  *************  MOBILE_MONEY  *************  ...          <NA>          <NA>            None            <NA>        None           <NA>  User Modification  "{\"notificationEndpointRequests\":[{\"notific...  "{\"notificationEndpointRequests\":[{\"notific...       <NA>          <NA>
2       UM.3  2025-05-01           DNI  ********  ***********     <NA>  FCOMPARTAMOS  ****************  *************  MOBILE_MONEY  *************  ...          <NA>          <NA>             DNI            <NA>    ********           <NA>  User Modification  "{\"notificationEndpointRequests\":[{\"notific...  "{\"notificationEndpointRequests\":[{\"notific...       <NA>          <NA>
3       UM.4  2025-05-01           DNI  ********  ***********     <NA>  FCOMPARTAMOS  ****************  *************  MOBILE_MONEY  *************  ...          <NA>          <NA>             DNI            <NA>    ********           <NA>  User Modification  "{\"profileDetails\":[{\"identifierValue\":\"5...  "{\"profileDetails\":[{\"identifierValue\":\"5...       <NA>          <NA>
4       UM.5  2025-05-01          None      None  ***********     <NA>  FCOMPARTAMOS  ****************  *************  MOBILE_MONEY  *************  ...          <NA>          <NA>            None            <NA>        None           <NA>  User Modification  "{\"profileDetails\":[{\"identifierValue\":\"5...  "{\"profileDetails\":[{\"identifierValue\":\"5...       <NA>          <NA>

[5 rows x 34 columns]
2025-05-13 23:30:03,703 - INFO - Procesando fecha: 2025-05-02
2025-05-13 23:30:03,725 - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-05-13 23:30:03,768 - INFO - Ejecutando query: log_usuarios_all
2025-05-13 23:30:52,018 - INFO - Se encontraron 36 registros para procesar
2025-05-13 23:30:52,018 - INFO - Eliminando carpeta existente para evitar duplicados: REPORTE_LOG_USUARIOS/2025/05/02
2025-05-13 23:30:52,025 - INFO - Resultados exportados a REPORTE_LOG_USUARIOS/2025/05/02/REPORTE_LOG_USUARIO_2025-05-02_233003.parquet
2025-05-13 23:30:52,046 - INFO - Muestra de datos procesados:
  userHistId   createdOn TipoDocumento Documento       Msisdn  MsisdnB    BankDomain        created_by         userId   accountType      accountId  ... PerfilCuentaA perfilCuentaB  TipoDocumentoA  TipoDocumentoB  DocumentoB  NumDocumentoB        requestType                                            oldData                                            newData  userIdOld  accountIdOld
0       UM.1  2025-05-02           DNI  ********  ***********     <NA>  FCOMPARTAMOS  ****************  *************  MOBILE_MONEY  *************  ...          <NA>          <NA>             DNI            <NA>    ********           <NA>  User Modification  "{\"profileDetails\":[{\"identifierValue\":\"5...  "{\"profileDetails\":[{\"identifierValue\":\"5...       <NA>          <NA>
1       UM.2  2025-05-02           DNI  ********  ***********     <NA>  FCOMPARTAMOS  ****************  *************  MOBILE_MONEY  *************  ...          <NA>          <NA>             DNI            <NA>    ********           <NA>  User Modification  "{\"notificationEndpointRequests\":[{\"notific...  "{\"notificationEndpointRequests\":[{\"notific...       <NA>          <NA>
2       UM.3  2025-05-02          None      None  ***********     <NA>  FCOMPARTAMOS  ****************  *************  MOBILE_MONEY  *************  ...          <NA>          <NA>            None            <NA>        None           <NA>  User Modification  "{\"profileDetails\":[{\"identifierValue\":\"5...  "{\"profileDetails\":[{\"identifierValue\":\"5...       <NA>          <NA>
3       UM.4  2025-05-02          None      None  ***********     <NA>  FCOMPARTAMOS  ****************  *************  MOBILE_MONEY  *************  ...          <NA>          <NA>            None            <NA>        None           <NA>  User Modification  "{\"notificationEndpointRequests\":[{\"notific...  "{\"notificationEndpointRequests\":[{\"notific...       <NA>          <NA>
4       UM.5  2025-05-02           DNI  ********  ***********     <NA>  FCOMPARTAMOS  6537367771791603  *************  MOBILE_MONEY  *************  ...          <NA>          <NA>             DNI            <NA>    ********           <NA>  User Modification  "{\"profileDetails\":[{\"identifierValue\":\"5...  "{\"profileDetails\":[{\"identifierValue\":\"5...       <NA>          <NA>

[5 rows x 34 columns]
2025-05-13 23:30:52,067 - INFO - Procesando fecha: 2025-05-03
2025-05-13 23:30:52,089 - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-05-13 23:30:52,133 - INFO - Ejecutando query: log_usuarios_all
2025-05-13 23:31:40,161 - INFO - Se encontraron 29 registros para procesar
2025-05-13 23:31:40,161 - INFO - Eliminando carpeta existente para evitar duplicados: REPORTE_LOG_USUARIOS/2025/05/03
2025-05-13 23:31:40,168 - INFO - Resultados exportados a REPORTE_LOG_USUARIOS/2025/05/03/REPORTE_LOG_USUARIO_2025-05-03_233052.parquet
2025-05-13 23:31:40,189 - INFO - Muestra de datos procesados:
  userHistId   createdOn TipoDocumento Documento       Msisdn  MsisdnB    BankDomain        created_by         userId   accountType      accountId  ... PerfilCuentaA perfilCuentaB  TipoDocumentoA  TipoDocumentoB  DocumentoB  NumDocumentoB        requestType                                            oldData                                            newData  userIdOld  accountIdOld
0       UM.1  2025-05-03           DNI  ********  ***********     <NA>  FCOMPARTAMOS  ****************  *************  MOBILE_MONEY  *************  ...          <NA>          <NA>             DNI            <NA>    ********           <NA>  User Modification  "{\"notificationEndpointRequests\":[{\"notific...  "{\"notificationEndpointRequests\":[{\"notific...       <NA>          <NA>
1       UM.2  2025-05-03           DNI  ********  ***********     <NA>  FCOMPARTAMOS  ****************  *************  MOBILE_MONEY  *************  ...          <NA>          <NA>             DNI            <NA>    ********           <NA>  User Modification  "{\"notificationEndpointRequests\":[{\"notific...  "{\"notificationEndpointRequests\":[{\"notific...       <NA>          <NA>
2       UM.3  2025-05-03           DNI  ********  ***********     <NA>  FCOMPARTAMOS  ****************  *************  MOBILE_MONEY  *************  ...          <NA>          <NA>             DNI            <NA>    ********           <NA>  User Modification  "{\"notificationEndpointRequests\":[{\"notific...  "{\"notificationEndpointRequests\":[{\"notific...       <NA>          <NA>
3       UM.4  2025-05-03          None      None  51984028578     <NA>  FCOMPARTAMOS  ****************  4308123494255  MOBILE_MONEY  4308123494321  ...          <NA>          <NA>            None            <NA>        None           <NA>  User Modification  "{\"notificationEndpointRequests\":[{\"notific...  "{\"notificationEndpointRequests\":[{\"notific...       <NA>          <NA>
4       UM.5  2025-05-03          None      None  51947142790     <NA>  FCOMPARTAMOS  ****************  4280303610446  MOBILE_MONEY  4280303610540  ...          <NA>          <NA>            None            <NA>        None           <NA>  User Modification  "{\"notificationEndpointRequests\":[{\"notific...  "{\"notificationEndpointRequests\":[{\"notific...       <NA>          <NA>

[5 rows x 34 columns]
2025-05-13 23:31:40,209 - INFO - Procesando fecha: 2025-05-04
2025-05-13 23:31:40,231 - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-05-13 23:31:40,277 - INFO - Ejecutando query: log_usuarios_all
2025-05-13 23:32:27,280 - INFO - Se encontraron 16 registros para procesar
2025-05-13 23:32:27,280 - INFO - Eliminando carpeta existente para evitar duplicados: REPORTE_LOG_USUARIOS/2025/05/04
2025-05-13 23:32:27,286 - INFO - Resultados exportados a REPORTE_LOG_USUARIOS/2025/05/04/REPORTE_LOG_USUARIO_2025-05-04_233140.parquet
2025-05-13 23:32:27,307 - INFO - Muestra de datos procesados:
  userHistId   createdOn TipoDocumento Documento       Msisdn  MsisdnB    BankDomain        created_by         userId   accountType      accountId  ... PerfilCuentaA perfilCuentaB  TipoDocumentoA  TipoDocumentoB  DocumentoB  NumDocumentoB        requestType                                            oldData                                            newData  userIdOld  accountIdOld
0       UM.1  2025-05-04           DNI  ********  ***********     <NA>  FCOMPARTAMOS  ****************  *************  MOBILE_MONEY  *************  ...          <NA>          <NA>             DNI            <NA>    ********           <NA>  User Modification  "{\"notificationEndpointRequests\":[{\"notific...  "{\"notificationEndpointRequests\":[{\"notific...       <NA>          <NA>
1       UM.2  2025-05-04          None      None  ***********     <NA>  FCOMPARTAMOS  ****************  *************  MOBILE_MONEY  *************  ...          <NA>          <NA>            None            <NA>        None           <NA>  User Modification  "{\"notificationEndpointRequests\":[{\"notific...  "{\"notificationEndpointRequests\":[{\"notific...       <NA>          <NA>
2       UM.3  2025-05-04          None      None  ***********     <NA>  FCOMPARTAMOS  ****************  *************  MOBILE_MONEY  *************  ...          <NA>          <NA>            None            <NA>        None           <NA>  User Modification  "{\"notificationEndpointRequests\":[{\"notific...  "{\"notificationEndpointRequests\":[{\"notific...       <NA>          <NA>
3       UM.4  2025-05-04           DNI  75496440  51933628098     <NA>  FCOMPARTAMOS  ****************  4391689919088  MOBILE_MONEY  4391689919195  ...          <NA>          <NA>             DNI            <NA>    75496440           <NA>  User Modification  "{\"notificationEndpointRequests\":[{\"notific...  "{\"notificationEndpointRequests\":[{\"notific...       <NA>          <NA>
4       UM.5  2025-05-04           DNI  ********  ***********     <NA>  FCOMPARTAMOS  ****************  *************  MOBILE_MONEY  *************  ...          <NA>          <NA>             DNI            <NA>    ********           <NA>  User Modification  "{\"profileDetails\":[{\"identifierValue\":\"5...  "{\"profileDetails\":[{\"identifierValue\":\"5...       <NA>          <NA>

[5 rows x 34 columns]
2025-05-13 23:32:27,327 - INFO - Procesando fecha: 2025-05-05
2025-05-13 23:32:27,349 - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-05-13 23:32:27,392 - INFO - Ejecutando query: log_usuarios_all
2025-05-13 23:33:13,291 - ERROR - Error al procesar la fecha 2025-05-05: Query interrupted
2025-05-13 23:33:13,466 - INFO - Procesando fecha: 2025-05-06
2025-05-13 23:33:13,489 - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-05-13 23:33:13,535 - INFO - Ejecutando query: log_usuarios_all
