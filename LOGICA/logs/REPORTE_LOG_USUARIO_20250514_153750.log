2025-05-14 15:37:50,970 - INFO - Procesand<PERSON> datos para REPORTE_LOG_USUARIO desde 2025-05-05 hasta 2025-05-05
2025-05-14 15:37:50,970 - INFO - Procesando fecha: 2025-05-05
2025-05-14 15:37:50,998 - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-05-14 15:37:51,039 - INFO - Ejecutando query: log_usuarios_all
2025-05-14 15:39:58,659 - WARNING - Error al ejecutar la query original: Conversion Error: Malformed JSON at byte 0 of input: unexpected character.  Input: CIERRE POR APP BIM
2025-05-14 15:39:58,659 - INFO - Aplicando solución para manejar JSON como texto (VARCHAR)...
2025-05-14 15:42:11,853 - INFO - Solución para tratar JSON como texto aplicada con éxito. Se encontraron 13001 registros para procesar
2025-05-14 15:42:12,008 - INFO - Resultados exportados a REPORTE_LOG_USUARIOS/2025/05/05/REPORTE_LOG_USUARIO_2025-05-05_153750.parquet
2025-05-14 15:42:12,033 - INFO - Muestra de datos procesados:
  userHistId   createdOn TipoDocumento Documento       Msisdn  ...        requestType                                            oldData                                            newData userIdOld accountIdOld
0    UM.4417  2025-05-05           DNI  ********  ***********  ...  User Modification  "{\"profileDetails\":[{\"identifierValue\":\"5...  "{\"profileDetails\":[{\"identifierValue\":\"5...      None         None
1    UM.4299  2025-05-05          None      None  ***********  ...  User Modification  "{\"profileDetails\":[{\"identifierValue\":\"5...  "{\"profileDetails\":[{\"identifierValue\":\"5...   2456994      3352401
2    UM.2210  2025-05-05           DNI  ********  ***********  ...  User Modification  "{\"profileDetails\":[{\"identifierValue\":\"5...  "{\"profileDetails\":[{\"identifierValue\":\"5...      None         None
3    UM.2211  2025-05-05           DNI  ********  ***********  ...  User Modification  "{\"profileDetails\":[{\"identifierValue\":\"5...  "{\"profileDetails\":[{\"identifierValue\":\"5...      None         None
4       UM.5  2025-05-05           DNI  ********  ***********  ...  User Modification  "{\"notificationEndpointRequests\":[{\"notific...  "{\"notificationEndpointRequests\":[{\"notific...      None         None

[5 rows x 34 columns]
2025-05-14 15:42:12,033 - INFO - Ejecutando script de post-procesamiento: REPORTES/REPORTE_LOG_USUARIO/post_process.py 2025-05-05
2025-05-14 15:42:13,733 - INFO - Script de post-procesamiento ejecutado con éxito: REPORTES/REPORTE_LOG_USUARIO/post_process.py
2025-05-14 15:42:13,792 - INFO - Proceso completado
