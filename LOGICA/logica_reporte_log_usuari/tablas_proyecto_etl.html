<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tablas del Proyecto ETL de Logs de Usuarios</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #2980b9;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 10px;
        }
        h3 {
            color: #16a085;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
        }
        th, td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f1f1f1;
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        .note {
            background-color: #e7f4ff;
            padding: 10px;
            border-left: 4px solid #3498db;
            margin: 15px 0;
        }
        footer {
            margin-top: 50px;
            text-align: center;
            font-size: 0.9em;
            color: #7f8c8d;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Tablas del Proyecto ETL de Logs de Usuarios</h1>
    
    <div class="section">
        <h2>1. Base de Datos MySQL</h2>
        <table>
            <thead>
                <tr>
                    <th>Base de Datos</th>
                    <th>Esquema</th>
                    <th>Tabla</th>
                    <th>Descripción</th>
                    <th>Utilizada en</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>MySQL</td>
                    <td>app_bim_prod_1</td>
                    <td>user_account_history</td>
                    <td>Almacena el historial de cambios en cuentas de usuarios</td>
                    <td>SP_LOG_USR, mysql_extract.py</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="section">
        <h2>2. Base de Datos Oracle</h2>
        
        <h3>2.1. Esquema USR_DATALAKE (Tablas de Trabajo)</h3>
        <table>
            <thead>
                <tr>
                    <th>Base de Datos</th>
                    <th>Esquema</th>
                    <th>Tabla</th>
                    <th>Descripción</th>
                    <th>Utilizada en</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Oracle</td>
                    <td>USR_DATALAKE</td>
                    <td>USER_DATA_TRX</td>
                    <td>Tabla temporal para datos de usuarios</td>
                    <td>SP_PRE_LOG_USR, SP_LOG_USR</td>
                </tr>
                <tr>
                    <td>Oracle</td>
                    <td>USR_DATALAKE</td>
                    <td>USER_MODIFICATION_DAY</td>
                    <td>Almacena modificaciones diarias de usuarios</td>
                    <td>SP_USER_MODIFICATION, SP_LOG_USR</td>
                </tr>
                <tr>
                    <td>Oracle</td>
                    <td>USR_DATALAKE</td>
                    <td>USER_AUTH_CHANGE_HISTORY</td>
                    <td>Almacena cambios de autenticación de usuarios</td>
                    <td>SP_USER_AUTH_DAY, SP_LOG_USR</td>
                </tr>
                <tr>
                    <td>Oracle</td>
                    <td>USR_DATALAKE</td>
                    <td>USER_ACCOUNT_HISTORY</td>
                    <td>Almacena historial de cuentas de usuarios</td>
                    <td>SP_LOG_USR, oracle_load.py</td>
                </tr>
                <tr>
                    <td>Oracle</td>
                    <td>USR_DATALAKE</td>
                    <td>LOG_USR</td>
                    <td>Tabla final de logs de usuarios</td>
                    <td>SP_LOG_USR, LOG-USUARIOS.sql</td>
                </tr>
                <tr>
                    <td>Oracle</td>
                    <td>USR_DATALAKE</td>
                    <td>PRE_LOG_TRX</td>
                    <td>Tabla temporal para datos de transacciones</td>
                    <td>SP_PRE_LOG_TRX, SP_LOG_TRX</td>
                </tr>
                <tr>
                    <td>Oracle</td>
                    <td>USR_DATALAKE</td>
                    <td>LOG_TRX_FINAL</td>
                    <td>Tabla final de logs de transacciones</td>
                    <td>SP_LOG_TRX, LOG-TRANSACCIONES.sql</td>
                </tr>
            </tbody>
        </table>

        <h3>2.2. Esquema PDP_PROD10_MAINDB (Tablas de Origen)</h3>
        <table>
            <thead>
                <tr>
                    <th>Base de Datos</th>
                    <th>Esquema</th>
                    <th>Tabla</th>
                    <th>Descripción</th>
                    <th>Utilizada en</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Oracle</td>
                    <td>PDP_PROD10_MAINDB</td>
                    <td>USER_PROFILE</td>
                    <td>Perfiles de usuarios</td>
                    <td>SP_PRE_LOG_USR</td>
                </tr>
                <tr>
                    <td>Oracle</td>
                    <td>PDP_PROD10_MAINDB</td>
                    <td>KYC_DETAILS</td>
                    <td>Detalles de KYC (Know Your Customer)</td>
                    <td>SP_PRE_LOG_USR</td>
                </tr>
                <tr>
                    <td>Oracle</td>
                    <td>PDP_PROD10_MAINDB</td>
                    <td>ISSUER_DETAILS</td>
                    <td>Detalles de emisores</td>
                    <td>SP_PRE_LOG_USR, SP_LOG_USR</td>
                </tr>
                <tr>
                    <td>Oracle</td>
                    <td>PDP_PROD10_MAINDB</td>
                    <td>MTX_CATEGORIES</td>
                    <td>Categorías de transacciones</td>
                    <td>SP_PRE_LOG_USR</td>
                </tr>
                <tr>
                    <td>Oracle</td>
                    <td>PDP_PROD10_MAINDB</td>
                    <td>CHANNEL_GRADES</td>
                    <td>Grados de canales</td>
                    <td>SP_PRE_LOG_USR</td>
                </tr>
                <tr>
                    <td>Oracle</td>
                    <td>PDP_PROD10_MAINDB</td>
                    <td>USER_MODIFICATION_HISTORY</td>
                    <td>Historial de modificaciones de usuarios</td>
                    <td>SP_USER_MODIFICATION</td>
                </tr>
                <tr>
                    <td>Oracle</td>
                    <td>PDP_PROD10_MAINDB</td>
                    <td>USER_AUTH_CHANGE_HISTORY</td>
                    <td>Historial de cambios de autenticación</td>
                    <td>SP_USER_AUTH_DAY</td>
                </tr>
                <tr>
                    <td>Oracle</td>
                    <td>PDP_PROD10_MAINDB</td>
                    <td>USER_IDENTIFIER</td>
                    <td>Identificadores de usuarios</td>
                    <td>SP_LOG_USR</td>
                </tr>
                <tr>
                    <td>Oracle</td>
                    <td>PDP_PROD10_MAINDB</td>
                    <td>SYS_SERVICE_TYPES</td>
                    <td>Tipos de servicios del sistema</td>
                    <td>SP_PRE_LOG_TRX</td>
                </tr>
                <tr>
                    <td>Oracle</td>
                    <td>PDP_PROD10_MAINDB</td>
                    <td>SYS_SERVICE_PROVIDER</td>
                    <td>Proveedores de servicios del sistema</td>
                    <td>SP_PRE_LOG_TRX</td>
                </tr>
            </tbody>
        </table>

        <h3>2.3. Esquema PDP_PROD10_MAINDBBUS (Tablas de Origen)</h3>
        <table>
            <thead>
                <tr>
                    <th>Base de Datos</th>
                    <th>Esquema</th>
                    <th>Tabla</th>
                    <th>Descripción</th>
                    <th>Utilizada en</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Oracle</td>
                    <td>PDP_PROD10_MAINDBBUS</td>
                    <td>MTX_WALLET</td>
                    <td>Billeteras de transacciones</td>
                    <td>SP_PRE_LOG_USR</td>
                </tr>
                <tr>
                    <td>Oracle</td>
                    <td>PDP_PROD10_MAINDBBUS</td>
                    <td>MTX_TRANSACTION_HEADER</td>
                    <td>Cabeceras de transacciones</td>
                    <td>SP_PRE_LOG_TRX</td>
                </tr>
                <tr>
                    <td>Oracle</td>
                    <td>PDP_PROD10_MAINDBBUS</td>
                    <td>MTX_TRANSACTION_ITEMS</td>
                    <td>Ítems de transacciones</td>
                    <td>SP_PRE_LOG_TRX</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="section">
        <h2>3. Tablas Temporales y CTEs (Common Table Expressions)</h2>
        <table>
            <thead>
                <tr>
                    <th>Tipo</th>
                    <th>Nombre</th>
                    <th>Descripción</th>
                    <th>Utilizada en</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>CTE</td>
                    <td>WALLETS</td>
                    <td>Información de billeteras</td>
                    <td>SP_PRE_LOG_USR</td>
                </tr>
                <tr>
                    <td>CTE</td>
                    <td>WALLET_OLD</td>
                    <td>Información histórica de billeteras</td>
                    <td>SP_LOG_USR</td>
                </tr>
                <tr>
                    <td>CTE</td>
                    <td>PROCESS</td>
                    <td>Procesamiento de datos de usuarios</td>
                    <td>SP_LOG_USR</td>
                </tr>
                <tr>
                    <td>CTE</td>
                    <td>TRX_HEADER</td>
                    <td>Cabeceras de transacciones</td>
                    <td>SP_PRE_LOG_TRX</td>
                </tr>
                <tr>
                    <td>CTE</td>
                    <td>TRX_ITEMS</td>
                    <td>Ítems de transacciones</td>
                    <td>SP_PRE_LOG_TRX</td>
                </tr>
                <tr>
                    <td>CTE</td>
                    <td>USER_DATA</td>
                    <td>Datos de usuarios</td>
                    <td>SP_PRE_LOG_TRX</td>
                </tr>
                <tr>
                    <td>CTE</td>
                    <td>MTI_SCP</td>
                    <td>Transacciones SCP</td>
                    <td>SP_PRE_LOG_TRX</td>
                </tr>
                <tr>
                    <td>CTE</td>
                    <td>PRE_LOG</td>
                    <td>Datos previos de logs</td>
                    <td>SP_LOG_TRX</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="section">
        <h2>4. Parámetros de Conexión</h2>
        <table>
            <thead>
                <tr>
                    <th>Tipo</th>
                    <th>Parámetro</th>
                    <th>Valor</th>
                    <th>Utilizado en</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>MySQL</td>
                    <td>MYSQL_HOST</td>
                    <td>db-mid-prd-mysql-dbawspdp.chy48i8i0qi1.us-east-1.rds.amazonaws.com</td>
                    <td>db_connections.py</td>
                </tr>
                <tr>
                    <td>MySQL</td>
                    <td>MYSQL_PORT</td>
                    <td>3306</td>
                    <td>db_connections.py</td>
                </tr>
                <tr>
                    <td>MySQL</td>
                    <td>MYSQL_USER</td>
                    <td>usr_datalake</td>
                    <td>db_connections.py</td>
                </tr>
                <tr>
                    <td>MySQL</td>
                    <td>MYSQL_DB</td>
                    <td>app_bim_prod_1</td>
                    <td>db_connections.py</td>
                </tr>
                <tr>
                    <td>Oracle</td>
                    <td>ORACLE_HOST</td>
                    <td>*************</td>
                    <td>db_connections.py</td>
                </tr>
                <tr>
                    <td>Oracle</td>
                    <td>ORACLE_PORT</td>
                    <td>1521</td>
                    <td>db_connections.py</td>
                </tr>
                <tr>
                    <td>Oracle</td>
                    <td>ORACLE_SERVICE</td>
                    <td>MMONEY</td>
                    <td>db_connections.py</td>
                </tr>
                <tr>
                    <td>Oracle</td>
                    <td>ORACLE_USER</td>
                    <td>usr_datalake</td>
                    <td>db_connections.py</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="section">
        <h2>5. Flujo de Datos</h2>
        <ol>
            <li>
                <strong>Extracción:</strong>
                <ul>
                    <li>Se extraen datos de MySQL (tabla <code>app_bim_prod_1.user_account_history</code>)</li>
                    <li>Se cargan en Oracle (tabla <code>USR_DATALAKE.USER_ACCOUNT_HISTORY</code>)</li>
                </ul>
            </li>
            <li>
                <strong>Transformación:</strong>
                <ul>
                    <li>SP_PRE_LOG_USR: Prepara datos en <code>USR_DATALAKE.USER_DATA_TRX</code></li>
                    <li>SP_USER_MODIFICATION: Procesa modificaciones en <code>USR_DATALAKE.USER_MODIFICATION_DAY</code></li>
                    <li>SP_USER_AUTH_DAY: Procesa autenticaciones en <code>USR_DATALAKE.USER_AUTH_CHANGE_HISTORY</code></li>
                    <li>SP_LOG_USR: Genera log final en <code>USR_DATALAKE.LOG_USR</code></li>
                </ul>
            </li>
            <li>
                <strong>Carga:</strong>
                <ul>
                    <li>Se exportan datos de <code>USR_DATALAKE.LOG_USR</code> a archivos CSV</li>
                    <li>Se procesan y firman los archivos CSV</li>
                    <li>Se suben a Amazon S3</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="section">
        <h2>6. Observaciones</h2>
        <div class="note">
            <ul>
                <li>El esquema <code>USR_DATALAKE</code> contiene principalmente tablas de trabajo y resultados finales.</li>
                <li>Los esquemas <code>PDP_PROD10_MAINDB</code> y <code>PDP_PROD10_MAINDBBUS</code> contienen las tablas de origen con los datos operativos.</li>
                <li>Existe un flujo similar para transacciones (SP_PRE_LOG_TRX y SP_LOG_TRX) que sigue un patrón similar al de usuarios.</li>
                <li>Las tablas temporales y CTEs se utilizan para procesar datos intermedios y mejorar la legibilidad del código.</li>
            </ul>
        </div>
    </div>

    <footer>
        <p>© 2025 Giancarlos Cardenas Galarza.</p>
    </footer>
</body>
</html>
