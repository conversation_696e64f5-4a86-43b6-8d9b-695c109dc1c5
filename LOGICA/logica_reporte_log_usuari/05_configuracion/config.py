from os import environ
from datetime import datetime
# MySQL connection parameters
MYSQL_HOST = environ.get('MYSQL_HOST', 'db-mid-prd-mysql-dbawspdp.chy48i8i0qi1.us-east-1.rds.amazonaws.com')
MYSQL_PORT = int(environ.get('MYSQL_PORT', '3306'))
MYSQL_USER = environ.get('MYSQL_USER', 'usr_datalake')
MYSQL_PASS = environ.get('MYSQL_PASS', 'U2024b1mD4t4l4k5')
MYSQL_DB = environ.get('MYSQL_DB', 'app_bim_prod_1')

ORACLE_HOST = environ.get('ORACLE_HOST', '*************')
#ORACLE_HOST = environ.get('ORACLE_HOST', '*************')
ORACLE_PORT = int(environ.get('OR<PERSON>LE_PORT', '1521'))
ORACLE_SERVICE = environ.get('ORACLE_SERVICE', 'MMONEY')
ORACLE_USER = environ.get('ORACLE_USER', 'usr_datalake')
ORACLE_PASS = environ.get('ORACLE_PASS', 'U2024b1mD4t4l4k5')

OUTPUT_DIR = environ.get('OUTPUT_DIR', '/home/<USER>/output/csv/')

FILE_PREFIX = 'user_account_history'
FILE_DATE = datetime.now().strftime('%Y%m%d')
FILE_NAME = f"{FILE_PREFIX}_{FILE_DATE}.csv"
