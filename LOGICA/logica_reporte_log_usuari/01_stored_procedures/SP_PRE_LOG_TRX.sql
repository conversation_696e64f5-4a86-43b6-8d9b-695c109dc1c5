
  CREATE OR REPLACE EDITIONABLE PROCEDURE "USR_D<PERSON><PERSON><PERSON>KE"."SP_PRE_LOG_TRX" (PARAM_FECHA IN VARCHAR, OUT_RESULTADO OUT VARCHAR -- Par<PERSON><PERSON><PERSON> de salida
)
IS
    v_count INTEGER;
BEGIN

--------------------------------------------------------------------------------------------------------------	
-----------------------------------------QUERY DE PRE LOG DE TRX  ---------------------------------------------
	
	SELECT COUNT(*) 
    INTO v_count
    FROM USR_DATALAKE.PRE_LOG_TRX
    WHERE TRUNC("TransferDate") = TRUNC(TO_DATE(PARAM_FECHA, 'YYYY-MM-DD HH24:MI:SS'));
    
    DBMS_OUTPUT.PUT_LINE('v_count: ' || v_count);  -- Imprimir la cantidad de registros

    -- Si no hay filas que cumplan con la condición, salir del procedimiento
    IF v_count <> 0 THEN
        OUT_RESULTADO := 'El procedimiento ya ha sido procesado';
        DBMS_OUTPUT.PUT_LINE('El SP ya ha sido procesado');
        RETURN;  -- No hacer nada si no hay filas que procesar
    END IF;
	
	--EXECUTE IMMEDIATE 'DELETE FROM USR_DATALAKE.PRE_LOG_TRX WHERE TRUNC("TransferDate") = (SELECT MAX(TRUNC("TransferDate")) FROM USR_DATALAKE.PRE_LOG_TRX) -31';
	
	INSERT INTO USR_DATALAKE.PRE_LOG_TRX
	WITH
	TRX_HEADER AS (
		SELECT 
			MTH.*,
			REPLACE(MTH.PAYEE_IDENTIFIER_VALUE,'1','') AS NEW_PAYEE_IDENTIFIER_VALUE,
			REPLACE(MTH.PAYER_IDENTIFIER_VALUE,'1','') AS NEW_PAYER_IDENTIFIER_VALUE
		FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER MTH
		LEFT JOIN PDP_PROD10_MAINDB.SYS_SERVICE_TYPES SST ON MTH.SERVICE_TYPE = SST.SERVICE_TYPE 
		WHERE MTH.TRANSFER_DATE >= TO_DATE(param_fecha, 'YYYY-MM-DD HH24:MI:SS')
		AND MTH.TRANSFER_DATE < TO_DATE(param_fecha, 'YYYY-MM-DD HH24:MI:SS')+1
		AND MTH.TRANSFER_STATUS IN ('TA','TS')
		AND MTH.TRANSFER_VALUE <> 0
		AND SST.IS_FINANCIAL = 'Y'
	),
	TRX_ITEMS AS (
		SELECT 
			MTI.TRANSFER_ID, MTI.TRANSFER_VALUE,MTI.WALLET_NUMBER, MTI.SECOND_PARTY_WALLET_NUMBER, MTI.SERVICE_TYPE,
			MTI.ISSUER_ID, MTI.SECOND_PARTY_ISSUER_ID,MTI.SECOND_PARTY_MARKETING_PROFILE_CODE, MTI.TRANSACTION_TYPE
		FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_ITEMS MTI
		INNER JOIN TRX_HEADER MTH ON MTI.TRANSFER_ID = MTH.TRANSFER_ID
	),
	USER_DATA AS (
		SELECT 
			USER_ID,
			O_USER_ID AS M_USER_ID,
			PROFILE_TRX AS PROFILE,
			WALLET_NUMBER AS ACCOUNT_ID,
			MSISDN,
			USER_CODE,
			LOGIN_ID,
			WORKSPACE_ID,
			ISSUER_CODE,
			ID_TYPE
		FROM USR_DATALAKE.USER_DATA_TRX
	),
	MTI_SCP AS (
		SELECT MTI.TRANSFER_ID, MTI.TRANSFER_VALUE
		FROM TRX_ITEMS MTI
		WHERE MTI.TRANSACTION_TYPE = 'SCP'
	),
	WALLETS_GRADE AS (
	SELECT MW.ISSUER_ID,MW.WALLET_NUMBER, CG.GRADE_NAME
	FROM PDP_PROD10_MAINDBBUS.MTX_WALLET MW 
	INNER JOIN PDP_PROD10_MAINDB.CHANNEL_GRADES CG ON MW.USER_GRADE = CG.GRADE_CODE
	),
	MTI_MP AS (
		SELECT MTI.TRANSFER_ID, MTI.WALLET_NUMBER, MTI.SECOND_PARTY_WALLET_NUMBER, MTI.ISSUER_ID, MTI.SECOND_PARTY_ISSUER_ID, WG.GRADE_NAME AS GRADE, WG2.GRADE_NAME AS SECOND_GRADE
		FROM TRX_ITEMS MTI
		LEFT JOIN WALLETS_GRADE WG ON MTI.WALLET_NUMBER = WG.WALLET_NUMBER
		LEFT JOIN WALLETS_GRADE WG2 ON MTI.SECOND_PARTY_WALLET_NUMBER = WG2.WALLET_NUMBER
		WHERE MTI.TRANSACTION_TYPE = 'MP'
	),
	MTI_MR AS (
		SELECT 
			MTI.TRANSFER_ID, MTI.WALLET_NUMBER, MTI.ISSUER_ID, 
			CASE 
				WHEN MC.CATEGORY_NAME = 'Final User' THEN 'USUARIO FINAL'
				WHEN MC.CATEGORY_NAME = 'BIMER User' THEN 'BIMER'
			END AS PAYER_CATEGORY_CODE
		FROM TRX_ITEMS MTI
		INNER JOIN PDP_PROD10_MAINDB.MARKETING_PROFILE MP ON MTI.SECOND_PARTY_MARKETING_PROFILE_CODE = MP.MARKETING_PROFILE_CODE
		INNER JOIN PDP_PROD10_MAINDB.MTX_CATEGORIES MC ON MP.CATEGORY_CODE = MC.CATEGORY_CODE
		WHERE MTI.TRANSACTION_TYPE = 'MR'
		AND MTI.SERVICE_TYPE IN ('ISSUERMOV','CHANGECAT')
	),
	REVERSAL AS (
		SELECT MTH.TRANSFER_ID, MTH.FIELD7 
		FROM TRX_HEADER mth
	),
	TRX_DATA_DAY AS (
		SELECT
			MTH.FIELD7,
			MTH.TRANSFER_ID,
		    MTH.FTXN_ID,
			MTH.SOURCE,
			CASE WHEN MTH.PAYER_USER_ID = 'IND012' THEN MTH.PAYER_IDENTIFIER_VALUE ELSE MTH.PAYER_USER_ID END AS PAYER_USER_ID,
			MTH.PAYEE_USER_ID,
			MTH.NEW_PAYEE_IDENTIFIER_VALUE AS PAYEE_IDENTIFIER_VALUE,
			MTH.NEW_PAYER_IDENTIFIER_VALUE AS PAYER_IDENTIFIER_VALUE ,
			MTH.CREATED_BY ,
			MTH.MODIFIED_BY,
			MTH.TRANSFER_DATE,
			MTH.TRANSFER_STATUS,
			MTH.TRANSFER_VALUE,
			MSC.TRANSFER_VALUE AS FEE,
			MR.PAYER_CATEGORY_CODE,
			MTH.REQUEST_GATEWAY_TYPE AS CANAL,
			CASE 
				WHEN MTH.NEW_PAYEE_IDENTIFIER_VALUE IN ('claro','sentinel') THEN JSON_VALUE(MTH.PARTNER_DATA , '$.codigoPago')
				WHEN MTH.NEW_PAYEE_IDENTIFIER_VALUE = 'bitel' THEN SUBSTR(FIELD8,1,INSTR(FIELD8,'@')-1)
				WHEN MTH.NEW_PAYEE_IDENTIFIER_VALUE = 'unique' THEN SUBSTR(MTH.REMARKS,1,INSTR(MTH.REMARKS,'_')-1)
				ELSE MTH.REMARKS
			END AS REMARKS,
			MTH.REQUEST_GATEWAY_TYPE,
			MP.WALLET_NUMBER AS PAYER_WALLET_NUMBER,
			CASE 
				WHEN MTH.SERVICE_TYPE IN ('ISSUERMOV','CHANGECAT') THEN MR.WALLET_NUMBER
				ELSE MP.SECOND_PARTY_WALLET_NUMBER 
			END AS PAYEE_WALLET_NUMBER,
			UPPER(MP.GRADE) AS PAYER_GRADE,
			UPPER(MP.SECOND_GRADE) AS PAYEE_GRADE,
			ID1.ISSUER_CODE AS PAYER_ISSUER_CODE,
			ID2.ISSUER_CODE AS PAYEE_ISSUER_CODE,
			MTH.PAYER_PROVIDER_ID,
			MTH.RECONCILIATION_BY,
			MTH.FIELD2,
			CASE
				WHEN mth.service_type='CASHIN' AND mth.MODIFIED_BY <> 'IND012' THEN 'CASH_IN' 
				WHEN mth.service_type='CASHOUT' THEN 'CASH_OUT' 
				WHEN mth.service_type='P2P' THEN 'TRANSFER' 
				WHEN (mth.service_type='BILLPAY' AND (mth.PAYEE_IDENTIFIER_VALUE = 'crandes' OR (mth.payer_user_id in ('466787','580943','1597312','1885838','US.****************','US.****************','US.****************') or mth.payee_user_id in ('466787','580943','1597312','1885838','US.****************','US.****************','US.****************')))) THEN 'PAYMENT' 
				WHEN (mth.service_type='BILLPAY' AND (mth.PAYEE_IDENTIFIER_VALUE <> 'crandes' OR (mth.payer_user_id not in ('466787','580943','1597312','1885838','US.****************','US.****************','US.****************') and mth.payee_user_id not in ('466787','580943','1597312','1885838','US.****************','US.****************','US.****************')))) THEN 'EXTERNAL_PAYMENT' 
				WHEN mth.service_type='OFFUSOUT' THEN 'PAYMENT' 
				WHEN mth.service_type in ('STOCKCRT','STOCKINT','STOCK') THEN 'DEPOSIT' 
				WHEN mth.service_type in ('C2C','INVC2C') THEN 'FLOAT_TRANSFER' 
				WHEN mth.service_type in ('MULTIDRCR2') OR (MTH.SERVICE_TYPE = 'CASHIN' AND MTH.MODIFIED_BY = 'IND012') THEN 'BATCH_TRANSFER' 
				WHEN mth.service_type in ('OPTW') THEN 'TRANSFER_TO_ANY_BANK_ACCOUNT' 
				WHEN mth.service_type in ('STOCKTFR') THEN 'CUSTODY_ACCOUNTS_TRANSFER' 
				WHEN mth.service_type in ('ATMCASHOUT') THEN 'CASH_OUT_ATM' 
				WHEN mth.service_type in ('ISSUERMOV','CHANGECAT') THEN 'ADJUSTMENT'
				WHEN mth.service_type in ('TXNCORRECT') and mth.ATTR_3_NAME = 'BULK_PAYMENT_BATCHID' THEN 'REVERSAL_BATCH_TRANSFER'
				WHEN mth.service_type in ('TXNCORRECT') and (mth.ATTR_3_VALUE IN ('CASHOUT','CASHIN','ATMCASHOUT') OR MTH.ATTR_3_VALUE IS NULL) THEN 'REVERSAL'
				WHEN mth.service_type in ('TXNCORRECT') and mth.ATTR_3_VALUE='BILLPAY' THEN CASE 
					WHEN (mth.PAYEE_IDENTIFIER_VALUE = 'crandes' OR (mth.payer_user_id in ('466787','580943','1597312','1885838','US.****************','US.****************','US.****************') or mth.payee_user_id in ('466787','580943','1597312','1885838','US.****************','US.****************','US.****************'))) THEN 'TRANSFER' 
					ELSE 'REFUND' 
					END
				else mth.service_type
			END AS TRX_SERVICE
		FROM TRX_HEADER MTH
		LEFT JOIN MTI_MP MP ON MTH.TRANSFER_ID = MP.TRANSFER_ID
		LEFT JOIN MTI_MR MR ON MTH.TRANSFER_ID = MR.TRANSFER_ID
		LEFT JOIN MTI_SCP MSC ON MTH.TRANSFER_ID = MSC.TRANSFER_ID
		LEFT JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID1 ON MP.ISSUER_ID = ID1.ISSUER_ID
		LEFT JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID2 ON MP.SECOND_PARTY_ISSUER_ID = ID2.ISSUER_ID
	)
	SELECT 
		MTH.FIELD7,
		MTH.TRANSFER_ID,
		MTH.FTXN_ID,
		MTH.SOURCE,
		MTH.PAYER_USER_ID,
		MTH.PAYEE_USER_ID,
		MTH.PAYER_IDENTIFIER_VALUE,
		MTH.PAYEE_IDENTIFIER_VALUE,
		MTH.CREATED_BY,
		MTH.MODIFIED_BY,
		MTH.TRANSFER_DATE,
		MTH.TRANSFER_STATUS,
		MTH.TRANSFER_VALUE,
		MTH.FEE,
		MTH.CANAL,
		MTH.REMARKS,
		UPAYER.ACCOUNT_ID,
		UPAYEE.ACCOUNT_ID,
		MTH.PAYER_WALLET_NUMBER,
		MTH.PAYEE_WALLET_NUMBER,
		MTH.PAYER_GRADE,
		MTH.PAYEE_GRADE,
		MTH.PAYER_ISSUER_CODE,
		MTH.PAYEE_ISSUER_CODE,
		MTH.PAYER_PROVIDER_ID,
		MTH.RECONCILIATION_BY,
		CASE 
			WHEN MTH.TRX_SERVICE IN ('REVERSAL_BATCH_TRANSFER') AND (UPAYER.LOGIN_ID = 'VIRTUALINTEROPCFBIM' OR UPAYEE.LOGIN_ID = 'VIRTUALINTEROPCFBIM') THEN 'REVERSAL'
			ELSE MTH.TRX_SERVICE
		END TRX_SERVICE,
		UPAYER.MSISDN AS PAYER_MSISDN,
		UPAYEE.MSISDN AS PAYEE_MSISDN,
		UPAYER.LOGIN_ID AS PAYER_USERNAME,
		UPAYEE.LOGIN_ID AS PAYEE_USERNAME,
		UPAYER.WORKSPACE_ID AS PAYER_WORKSPACE,
		UPAYEE.WORKSPACE_ID AS PAYEE_WORKSPACE,
		UPAYER.USER_ID AS NEW_PAYER_USER_ID,
		UPAYEE.USER_ID AS NEW_PAYEE_USER_ID,
		SSP.PROVIDER_NAME,
		CASE
			WHEN UPAYER.PROFILE LIKE '%PROVEEDOR%' THEN UPAYER.PROFILE
			WHEN MTH.TRX_SERVICE IN ('REFUND') THEN CASE WHEN UPAYER.PROFILE <> 'COMERCIO' THEN UPPER(MTH.PAYER_IDENTIFIER_VALUE) || ' ' || UPAYER.PROFILE ELSE 'FCOMPARTAMOS COMERCIO' END
			WHEN MTH.TRX_SERVICE = 'ADJUSTMENT' THEN  MTH.PAYER_ISSUER_CODE || ' ' || MTH.PAYER_CATEGORY_CODE
			ELSE MTH.PAYER_ISSUER_CODE || ' ' || UPAYER.PROFILE
		END AS PAYER_PROFILE,
		CASE 
			WHEN UPAYEE.PROFILE LIKE '%PROVEEDOR%' THEN UPAYEE.PROFILE
			ELSE MTH.PAYEE_ISSUER_CODE || ' ' || UPAYEE.PROFILE 
		END AS PAYEE_PROFILE,
		CASE 
			WHEN MTH.TRX_SERVICE IN ('REVERSAL','REFUND') then TP.FIELD7
			WHEN MTH.TRX_SERVICE IN ('REVERSAL_BATCH_TRANSFER') AND (UPAYER.LOGIN_ID = 'VIRTUALINTEROPCFBIM' OR UPAYEE.LOGIN_ID = 'VIRTUALINTEROPCFBIM') THEN TP.FIELD7
			ELSE ''
		END AS "COMMENT",
		CASE 
			WHEN MTH.TRX_SERVICE IN ('BATCH_TRANSFER','REVERSAL_BATCH_TRANSFER') THEN 'internal'
			WHEN MTH.TRX_SERVICE = 'TRANSFER_TO_ANY_BANK_ACCOUNT' THEN 'http-pdp'
			WHEN CANAL = 'WEB' AND MTH.TRX_SERVICE = 'CUSTODY_ACCOUNTS_TRANSFER' THEN 'http-adm'
			WHEN MTH.TRX_SERVICE = 'DEPOSIT' THEN 'http-adm'
			WHEN MTH.TRX_SERVICE = 'REFUND' THEN 'http-awspdp'
			WHEN MTH.TRX_SERVICE = 'FLOAT_TRANSFER' THEN 'http-partner'
			WHEN MTH.SOURCE IS NULL AND MTH.TRX_SERVICE = 'PAYMENT' THEN 'http-xml_awspdp'
			WHEN CANAL = 'WEB' AND MTH.TRX_SERVICE = 'TRANSFER' AND UPAYER.PROFILE = 'COMERCIO' THEN 'http-awspdp'
			WHEN CANAL = 'WEB' AND MTH.SOURCE IS NULL AND MTH.TRX_SERVICE = 'CASH_IN' AND UPAYER.PROFILE = 'SUPER AGENTE' AND UPAYEE.PROFILE IN ('USUARIO FINAL','BIMER') THEN 'http-fcompartamos_ofi'
			WHEN CANAL = 'WEB' AND MTH.TRX_SERVICE = 'CASH_IN' AND MTH.PAYER_ISSUER_CODE = 'CRANDES' AND UPAYER.PROFILE = 'AGENCIA' THEN 'http-partner'
			WHEN CANAL = 'WEB' AND MTH.TRX_SERVICE = 'REVERSAL' AND UPAYER.PROFILE IN ('USUARIO FINAL','BIMER') AND MTH.PAYEE_ISSUER_CODE = 'CRANDES' AND UPAYEE.PROFILE = 'AGENCIA' THEN 'http-partner'
			WHEN CANAL = 'WEB' AND MTH.TRX_SERVICE = 'CASH_IN' AND UPAYER.LOGIN_ID = 'COMPWVIRTUAL163' THEN 'http-fcompartamos_ofi'
			WHEN CANAL = 'WEB' AND MTH.TRX_SERVICE = 'CASH_OUT' AND MTH.PAYEE_ISSUER_CODE = 'CRANDES' AND UPAYEE.PROFILE = 'AGENCIA' THEN 'http-xml_ms'
			WHEN CANAL = 'WEB' AND MTH.TRX_SERVICE = 'REVERSAL' AND (UPAYEE.LOGIN_ID = 'VIRTUALINTEROPCFBIM' OR UPAYER.LOGIN_ID = 'VIRTUALINTEROPCFBIM') THEN 'http-fcompartamos_niubiz_interope'
			WHEN CANAL = 'WEB' AND MTH.TRX_SERVICE = 'REVERSAL' AND UPAYEE.LOGIN_ID = 'COMPWKASNET' THEN 'http-ci_kasnet_partner'
			WHEN CANAL = 'WEB' AND MTH.TRX_SERVICE = 'REVERSAL' AND UPAYER.LOGIN_ID = 'COMPWVIRTUAL163' THEN 'http-fcompartamos_simp'
			WHEN CANAL = 'WEB' AND MTH.TRX_SERVICE = 'REVERSAL' AND UPAYEE.LOGIN_ID = 'COMPWVIRTUAL163' THEN 'http-fcompartamos_app'
			WHEN CANAL = 'WEB' AND MTH.TRX_SERVICE = 'REVERSAL' AND UPAYER.LOGIN_ID = 'COMPWFULLCARGA' THEN 'http-fcompartamos_fullcarga'
			WHEN CANAL = 'WEB' AND MTH.TRX_SERVICE = 'REVERSAL' AND (UPAYEE.LOGIN_ID = 'VIRTUALINTEROPCFCCE' OR UPAYER.LOGIN_ID = 'VIRTUALINTEROPCFCCE') THEN 'http-fcompartamos_cce_interope'
			WHEN MTH.TRX_SERVICE = 'ADJUSTMENT' THEN 'http-awspdp'
			ELSE CASE
				WHEN MTH.SOURCE LIKE 'http-%' THEN MTH.SOURCE
				WHEN MTH.SOURCE IS NOT NULL THEN 'http-' || MTH.SOURCE
				ELSE MTH.SOURCE
			END
		END	AS CONTEXT,
		UPAYER.ID_TYPE,
		UPAYEE.ID_TYPE
	FROM TRX_DATA_DAY MTH
	LEFT JOIN USER_DATA UPAYER ON MTH.PAYER_USER_ID = UPAYER.M_USER_ID AND UPAYER.M_USER_ID IS NOT NULL
	LEFT JOIN USER_DATA UPAYEE ON MTH.PAYEE_USER_ID = UPAYEE.M_USER_ID AND UPAYEE.M_USER_ID IS NOT NULL
	LEFT JOIN PDP_PROD10_MAINDB.SYS_SERVICE_PROVIDER SSP ON MTH.PAYER_PROVIDER_ID = SSP.PROVIDER_ID
	LEFT JOIN REVERSAL TP ON MTH.RECONCILIATION_BY = TP.TRANSFER_ID AND MTH.TRX_SERVICE IN ('REVERSAL','REFUND','REVERSAL_BATCH_TRANSFER')
	WHERE 1=1
	AND MTH.TRX_SERVICE NOT IN ('MULTIDRCR','FTBOA','MERCHPAY','TXNCORRECT','MULTIDRCR3');

	COMMIT;
	
END SP_PRE_LOG_TRX;

