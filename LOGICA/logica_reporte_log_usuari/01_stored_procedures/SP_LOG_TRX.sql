
  CREATE OR REPLACE EDITIONABLE PROCEDURE "USR_D<PERSON><PERSON><PERSON>KE"."SP_LOG_TRX" (PARAM_FECHA IN VARCHAR)
AS
BEGIN

--------------------------------------------------------------------------------------------------------------	
-----------------------------------------QUERY DE LOG DE TRX  ---------------------------------------------
	DELETE FROM USR_DATALAKE.LOG_TRX_FINAL WHERE TRUNC(TO_DATE("DateTime",'YYYY-MM-DD HH24:MI:SS')) = TRUNC(TO_DATE(PARAM_FECHA,'YYYY-MM-DD HH24:MI:SS'));
	
	--DELETE FROM USR_DATALAKE.LOG_TRX_FINAL WHERE TRUNC(TO_DATE("DateTime",'YYYY-MM-DD HH24:MI:SS')) = (SELECT TRUNC(MAX(TO_DATE("DateTime",'YYYY-MM-DD HH24:MI:SS'))) FROM USR_DATALAKE.LOG_TRX_FINAL) -31;
	
	INSERT INTO USR_DATALAKE.LOG_TRX_FINAL
	WITH 
	PRE_LOG AS (
		SELECT "TransferID", "Context" 
		FROM USR_DATALAKE.PRE_LOG_TRX PL
	)
	SELECT 
		MTH."TransferID" AS "TransactionID",
		MTH."TransferID" AS "FinancialTransactionID",
		CASE 
			WHEN MTH."TransactionType" IN ('EXTERNAL_PAYMENT','DEPOSIT','BATCH_TRANSFER') THEN MTH."ExternalTransactionID"
		END AS "ExternalTransactionID",
		TO_CHAR(MTH."TransferDate", 'YYYY-MM-DD HH24:MI:SS') AS "DateTime",
		'ID:'|| REPLACE(MTH."CreatedBy",'US.','') ||'/MM' AS "InitiatingUser",
		CASE 
			WHEN MTH."TransactionType" = 'BATCH_TRANSFER' THEN 'ID:BatchTransferService/SERVICE'
			ELSE 'ID:'|| REPLACE(MTH."ModifiedBy",'US.','') ||'/MM' 
		END AS "RealUser",	
		CASE 
			WHEN MTH."TransactionType" IN ('DEPOSIT','CUSTODY_ACCOUNTS_TRANSFER') THEN ''
			WHEN H_PAYER.USER_ID IS NOT NULL AND H_PAYER.ACCOUNT_ID = MTH."From_AccountID_Mobiquity" THEN H_PAYER.ATTR7_OLD
			ELSE MTH."FromID" 
		END AS "FromID",
		CASE 
			WHEN mth."TransactionType" IN ('DEPOSIT','CUSTODY_ACCOUNTS_TRANSFER') THEN ''
			ELSE MTH."From_Msisdn"
		END AS "FromMSISDN",
		CASE 
			WHEN mth."TransactionType" IN ('DEPOSIT','CUSTODY_ACCOUNTS_TRANSFER') THEN ''
			WHEN MTH."From_Profile" LIKE '%BIMER%' OR MTH."From_Profile" LIKE '%FINAL%' THEN ''
			ELSE MTH."From_LoginID"
		END AS "FromUsername",
		CASE 
			WHEN mth."TransactionType" IN ('DEPOSIT','CUSTODY_ACCOUNTS_TRANSFER') THEN ''
			ELSE MTH."From_Profile"
		END AS "FromProfile",
		CASE 
			WHEN MTH."TransactionType" IN ('DEPOSIT','CUSTODY_ACCOUNTS_TRANSFER') THEN CASE
				WHEN MTH."From_BankDomain" = 'BNACION' THEN '1334853'
				WHEN MTH."From_BankDomain" = 'CCUSCO' THEN '1464437'
				WHEN MTH."From_BankDomain" = 'CRANDES' THEN '1414519'
				WHEN MTH."From_BankDomain" = '0231FCONFIANZA' THEN '1882233'
				WHEN MTH."From_BankDomain" = '0144QAPAQ' THEN '1131834'
				WHEN MTH."From_BankDomain" = 'FCOMPARTAMOS' THEN '1188057'
			END
			WHEN H_PAYER.USER_ID IS NOT NULL AND H_PAYER.ACCOUNT_ID = MTH."From_AccountID_Mobiquity" THEN H_PAYER.ATTR8_OLD
			ELSE MTH."From_AccountID" 
		END AS  "FromAccountID",
		'MM' AS "FromAccountType",
		COALESCE(MTH."Fee"/100,0) "FromFee",
		0 "FromLoyaltyReward",
		0 "FromLoyaltyFee",
		CASE
			WHEN MTH."TransactionType" IN ('CUSTODY_ACCOUNTS_TRANSFER','EXTERNAL_PAYMENT','TRANSFER_TO_ANY_BANK_ACCOUNT') THEN ''
			WHEN MTH."TransactionType" <> 'ADJUSTMENT' AND H_PAYEE.USER_ID IS NOT NULL AND H_PAYEE.ACCOUNT_ID = MTH."To_AccountID_Mobiquity" THEN H_PAYEE.ATTR7_OLD
			ELSE mth."ToID"
		END AS "ToID",
		CASE
			WHEN MTH."TransactionType" IN ('CUSTODY_ACCOUNTS_TRANSFER','EXTERNAL_PAYMENT','TRANSFER_TO_ANY_BANK_ACCOUNT') THEN ''
			ELSE mth."To_Msisdn"
		END AS "ToMSISDN",
		CASE
			WHEN MTH."TransactionType" IN ('CUSTODY_ACCOUNTS_TRANSFER','TRANSFER_TO_ANY_BANK_ACCOUNT') THEN ''
			WHEN MTH."TransactionType" = 'EXTERNAL_PAYMENT' THEN MTH."Remarks" ||'@'||MTH."To_Identifier" --QUITAR LUEGO
			WHEN MTH."To_Profile" LIKE '%BIMER%' OR MTH."To_Profile" LIKE '%FINAL%' THEN ''
			WHEN MTH."To_Workspace" = 'BUSINESS' THEN MTH."To_LoginID"
		END AS "ToUsername",
		CASE 
			WHEN MTH."TransactionType" IN ('EXTERNAL_PAYMENT','CUSTODY_ACCOUNTS_TRANSFER','TRANSFER_TO_ANY_BANK_ACCOUNT') THEN ''
			ELSE MTH."To_Profile"
		END AS "ToProfile",
		CASE 
			WHEN MTH."TransactionType" IN ('TRANSFER_TO_ANY_BANK_ACCOUNT','CUSTODY_ACCOUNTS_TRANSFER') THEN CASE
				WHEN MTH."To_BankDomain" = 'BNACION' THEN '1334853'
				WHEN MTH."To_BankDomain" = 'CCUSCO' THEN '1464437'
				WHEN MTH."To_BankDomain" = 'CRANDES' THEN '1414519'
				WHEN MTH."To_BankDomain" = '0231FCONFIANZA' THEN '1882233'
				WHEN MTH."To_BankDomain" = '0144QAPAQ' THEN '1131834'
				WHEN MTH."To_BankDomain" = 'FCOMPARTAMOS' THEN '1188057'
			END
			WHEN H_PAYEE.USER_ID IS NOT NULL AND H_PAYEE.ACCOUNT_ID = MTH."To_AccountID_Mobiquity" THEN H_PAYEE.ATTR8_OLD
			ELSE MTH."To_AccountID" 
		END AS "ToAccountID",
		'MM' AS "ToAccountType",
		0 "ToFee",
		0 "ToLoyaltyReward",
		0 "ToLoyaltyFee",
		MTH."TransactionType" "TransactionType",
		MTH."Amount"/100 "Amount",
		'PEN' AS "Currency",
		'COMMITTED' "TransactionStatus",
		CASE 
			WHEN PL."TransferID" IS NOT NULL THEN  PL."Context"
			ELSE MTH."Context" 
		END AS "Context",
		MTH."Comment" AS "Comment",
		MTH."From_BankDomain",
		MTH."To_BankDomain"
	FROM USR_DATALAKE.PRE_LOG_TRX MTH
	LEFT JOIN USER_ACCOUNT_HISTORY H_PAYER ON MTH."FromID_Mobiquity" = H_PAYER.USER_ID
	LEFT JOIN USER_ACCOUNT_HISTORY H_PAYEE ON MTH."ToID_Mobiquity" = H_PAYEE.USER_ID
	LEFT JOIN PRE_LOG PL ON MTH."Comment" = PL."TransferID" AND  "TransactionType" IN ('REVERSAL','REFUND')
	WHERE TRUNC("TransferDate") = TRUNC(TO_DATE(PARAM_FECHA, 'YYYY-MM-DD HH24:MI:SS'));
	COMMIT;
END SP_LOG_TRX;

