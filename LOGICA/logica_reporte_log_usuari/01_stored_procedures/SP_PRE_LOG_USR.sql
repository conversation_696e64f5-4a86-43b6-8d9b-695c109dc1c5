
  CREATE OR R<PERSON><PERSON>CE EDITIONABLE PROCEDURE "USR_<PERSON><PERSON><PERSON><PERSON><PERSON>"."SP_PRE_LOG_USR" (PARAM_FECHA IN VARCHAR)
IS 
BEGIN
---------------------------------------------------------------------------------------------------------------	
-----------------------------------------QUERY - PRE- LOG - USUARIOS  ------------------------------------------------
	
	EXECUTE IMMEDIATE 'TRUNCATE TABLE USR_DATALAKE.USER_DATA_TRX';
	
	INSERT INTO USR_DATALA<PERSON>.USER_DATA_TRX
	WITH 
	WALLETS AS (
	SELECT 
		MW.USER_ID,
		MW.WALLET_NUMBER,
		MW.ISSUER_ID,
		MW.USER_GRADE,
		ROW_NUMBER() OVER (PARTITION BY MW.USER_ID ORDER BY MW.MODIFIED_ON DESC) AS ORDEN
	FROM PDP_PROD10_MAINDBBUS.MTX_WALLET mw
	)
	SELECT 
			CASE 
				WHEN UP.ATTR7 IS NOT NULL THEN UP.ATTR7
				ELSE UP.USER_ID
			END AS USER_ID,
			UP.USER_ID AS O_USER_ID,
			CASE 
				WHEN UP.ATTR7 IS NOT NULL THEN UP.ATTR7
				WHEN LENGTH(REPLACE(UP.USER_ID,'US.',''))>15  THEN SUBSTR(REPLACE(UP.user_id,'US.',''),-15)
				ELSE REPLACE(UP.user_id,'US.','') END AS USER_ID_M,
			UK.ID_TYPE,
			UK.ID_VALUE,
			CASE 
				WHEN UP.ATTR8 IS NOT NULL THEN UP.ATTR8
				WHEN LENGTH(REPLACE(MW.WALLET_NUMBER,'UA.',''))>15 THEN SUBSTR(REPLACE(MW.WALLET_NUMBER,'UA.',''),-15) 
				ELSE REPLACE(MW.WALLET_NUMBER,'UA.','')
			END AS WALLET_NUMBER,
			UP.STATUS,
			CASE 
				WHEN UP.MSISDN IN ('***********','***********','***********','***********') THEN REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') || ' MERCHANT GENERAL ACCOUNT PROFILE'
				WHEN UP.MSISDN IN ('***********','***********','***********','***********','***********','***********','***********','***********','***********','***********','***********','***********') THEN UPPER(cg.grade_name) || ' PROFILE'
				ELSE REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','')  || ' ' || UPPER(cg.grade_name)
			END AS GRADE_NAME,
			UP.MSISDN,
			UP.CREATED_ON,
			UP.CREATED_BY,
			UP.REMARKS,
			UP.MODIFIED_ON AS STATUS_CHANGE_ON,
			REPLACE(ID.ISSUER_CODE,'0144','') AS ISSUER_CODE,
			UP.FIRST_NAME,
			UP.LAST_NAME,
			MC.CATEGORY_NAME,
			CASE 
				WHEN MC.CATEGORY_NAME = 'Final User' THEN ID.ISSUER_CODE || ' ' || 'USUARIO FINAL'
				WHEN MC.CATEGORY_NAME = 'BIMER User' THEN  ID.ISSUER_CODE || ' ' || 'BIMER'
				WHEN MC.CATEGORY_NAME = 'Virtual Agent' THEN ID.ISSUER_CODE || ' ' || 'AGENTE VIRTUAL'
				WHEN MC.CATEGORY_NAME = 'Agent' THEN ID.ISSUER_CODE || ' ' || 'AGENTE'
				WHEN MC.CATEGORY_NAME = 'Agencia' THEN ID.ISSUER_CODE || ' ' || 'AGENCIA'
				WHEN MC.CATEGORY_NAME = 'Super Agent' THEN ID.ISSUER_CODE || ' ' || 'SUPER AGENTE'
				WHEN UPPER(MC.CATEGORY_NAME) = 'REMESAS WU' THEN ID.ISSUER_CODE || ' ' || 'COMERCIO'
				WHEN MC.CATEGORY_NAME = 'Dispersor' THEN ID.ISSUER_CODE || ' ' || 'SUPER AGENTE'
				WHEN MC.CATEGORY_NAME = 'Biller' THEN CASE 
						WHEN up.MSISDN IN ('***********','***********','***********','***********') THEN ID.ISSUER_CODE || ' ' || 'COMERCIO'
						ELSE CASE
							WHEN UP.MSISDN = '***********' THEN 'LINDLEY PROVEEDOR DE SERVICIOS'
							WHEN UP.MSISDN = '***********' THEN 'BACKUS PROVEEDOR DE SERVICIOS'
							WHEN UP.MSISDN IN ('***********','***********') THEN 'BITEL PROVEEDOR DE SERVICIOS'
							WHEN UP.MSISDN IN ('***********','***********') THEN 'CLARO PROVEEDOR DE SERVICIOS'
							WHEN UP.MSISDN = '***********' THEN 'ENTEL DE SERVICIOS'
							WHEN UP.MSISDN = '***********' THEN 'GLORIA PROVEEDOR DE SERVICIOS'
							WHEN UP.MSISDN = '***********' THEN 'MOVISTAR PROVEEDOR DE SERVICIOS'
							WHEN UP.MSISDN = '***********' THEN 'UNIQUE PROVEEDOR DE SERVICIOS'
							WHEN UP.MSISDN = '***********' THEN 'DIGIFLOW PROVEEDOR DE SERVICIOS'
							WHEN UP.MSISDN = '***********' THEN 'SUNAT PROVEEDOR DE SERVICIOS'
						END 
					END
				ELSE ID.ISSUER_CODE || ' ' || MC.CATEGORY_NAME 
			END AS PROFILE,
			CASE 
				WHEN MC.CATEGORY_NAME = 'Final User' THEN 'USUARIO FINAL'
				WHEN MC.CATEGORY_NAME = 'BIMER User' THEN 'BIMER'
				WHEN MC.CATEGORY_NAME = 'Virtual Agent' THEN 'AGENTE VIRTUAL'
				WHEN MC.CATEGORY_NAME = 'Agent' THEN 'AGENTE'
				WHEN MC.CATEGORY_NAME = 'Agencia' THEN 'AGENCIA'
				WHEN MC.CATEGORY_NAME = 'Super Agent' THEN 'SUPER AGENTE'
				WHEN UPPER(MC.CATEGORY_NAME) = 'REMESAS WU' THEN 'COMERCIO'
				WHEN MC.CATEGORY_NAME = 'Dispersor' THEN 'SUPER AGENTE'
				WHEN MC.CATEGORY_NAME = 'Biller' THEN CASE 
						WHEN up.MSISDN IN ('***********','***********','***********','***********') THEN 'COMERCIO'
						ELSE CASE
							WHEN UP.MSISDN = '***********' THEN 'LINDLEY PROVEEDOR DE SERVICIOS'
							WHEN UP.MSISDN = '***********' THEN 'BACKUS PROVEEDOR DE SERVICIOS'
							WHEN UP.MSISDN IN ('***********','***********') THEN 'BITEL PROVEEDOR DE SERVICIOS'
							WHEN UP.MSISDN IN ('***********','***********') THEN 'CLARO PROVEEDOR DE SERVICIOS'
							WHEN UP.MSISDN = '***********' THEN 'ENTEL PROVEEDOR DE SERVICIOS'
							WHEN UP.MSISDN = '***********' THEN 'GLORIA PROVEEDOR DE SERVICIOS'
							WHEN UP.MSISDN = '***********' THEN 'MOVISTAR PROVEEDOR DE SERVICIOS'
							WHEN UP.MSISDN = '***********' THEN 'UNIQUE PROVEEDOR DE SERVICIOS'
							WHEN UP.MSISDN = '***********' THEN 'DIGIFLOW PROVEEDOR DE SERVICIOS'
							WHEN UP.MSISDN = '***********' THEN 'SUNAT PROVEEDOR DE SERVICIOS'
							WHEN UP.MSISDN = '51946594070' THEN 'SENTINEL PROVEEDOR DE SERVICIOS'
						END 
					END
				ELSE MC.CATEGORY_NAME 
			END AS PROFILE_TRX,
			UP.ATTR1,
			UP.PREFERRED_LANG,
			UP.USER_CODE,
			UP.LOGIN_ID,
			UP.WORKSPACE_ID
		FROM PDP_PROD10_MAINDB.USER_PROFILE UP  
		INNER JOIN PDP_PROD10_MAINDB.KYC_DETAILS UK ON UP.KYC_ID = UK.KYC_ID
		LEFT JOIN WALLETS MW ON UP.USER_ID = MW.USER_ID AND ORDEN=1
		LEFT JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON MW.ISSUER_ID = ID.ISSUER_ID 
		INNER JOIN PDP_PROD10_MAINDB.MTX_CATEGORIES MC ON UP.CATEGORY_ID = MC.CATEGORY_CODE
		LEFT JOIN PDP_PROD10_MAINDB.CHANNEL_GRADES CG ON MW.USER_GRADE = CG.GRADE_CODE
		WHERE TRUNC(UP.CREATED_ON) <= TRUNC(TO_DATE(PARAM_FECHA,'YYYY-MM-DD  HH24:MI:SS'));
	COMMIT;
END SP_PRE_LOG_USR;

