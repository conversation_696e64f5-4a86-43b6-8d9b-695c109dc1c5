
  CREATE OR <PERSON><PERSON><PERSON>CE EDITIONABLE PROCEDURE "USR_<PERSON><PERSON><PERSON><PERSON><PERSON>"."SP_USER_MODIFICATION" (PARAM_FECHA IN VARCHAR)
IS 
BEGIN
---------------------------------------------------------------------------------------------------------------	
-----------------------------------------QUERY - LOG - USUARIOS  ------------------------------------------------
	
	EXECUTE IMMEDIATE 'TRUNCATE TABLE USR_DATALAKE.USER_MODIFICATION_DAY';

	INSERT INTO USR_DATALA<PERSON>.USER_MODIFICATION_DAY
	SELECT 
		umh.REQUEST_TYPE,
		umh.old_data, 
		umh.new_data,
		JSON_VALUE(DBMS_LOB.SUBSTR(umh.new_data, 4000, 1), '$.profileDetails.remarks') AS razon,
		umh.user_id,
		umh.created_by,
		umh.created_on
	FROM PDP_PROD10_MAINDB.USER_MODIFICATION_HISTORY umh
	WHERE trunc(umh.CREATED_ON) = TRUNC(TO_DATE(PARAM_FECHA,'YYYY-MM-DD HH24:MI:SS'));

	COMMIT;

END SP_USER_MODIFICATION;

