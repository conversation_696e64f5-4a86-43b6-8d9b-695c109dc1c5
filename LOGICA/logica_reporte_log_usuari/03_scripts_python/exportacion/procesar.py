import logging
from logging.handlers import TimedRotatingFileHandler
import os
import sys
import boto3
import pandas as pd
import json
import re
import secrets
import time
from file_signer.file_signer import FileSigner
from datetime import datetime, timedelta
from tipos_transaccion_map import tipos_transaccion_map

from config import bucket_s3, input_path, private_key_path, public_key_path

# Configurar el logger
log_dir = 'logs'
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_filename = os.path.join(log_dir, 'output.log')
handler = TimedRotatingFileHandler(log_filename, when="midnight", interval=1)
handler.suffix = "%Y%m%d"
logging.basicConfig(level=logging.INFO, handlers=[handler])

# Función para subir archivos a S3
def upload_s3(file_route, file_name, s3_key):
    try:
        s3_client = boto3.client('s3')
        s3_client.upload_file(file_route, bucket_s3, f"{s3_key}{file_name}")
        logging.info(f"Archivo: {file_name} subido a: s3://{bucket_s3}/{s3_key}{file_name}")
    except Exception as e:
        logging.error(f"Error al subir archivo a S3: {str(e)}")

# Función para comparar datos antiguos y nuevos
def compare_data(old_data, new_data):
    differences = []
    
    # Recorrer todas las claves en old_data
    for key in old_data:
        # Si la clave existe en new_data y los valores son diferentes
        if key in new_data and old_data[key] != new_data[key]:
            differences.append({
                'campo': key,
                'old_value': old_data[key],
                'new_value': new_data[key]
            })
    
    return differences

# Función para obtener la clave final en caso de claves anidadas
def get_final_key(key):
    return key.split('.')[-1]

# Función para manejar valores especiales según REQUESTTYPE
def handle_request_type(request_type):
    if request_type == "Suspend User":
        return "BLOQUSR"
    elif request_type == "Lock Wallet":
        return "BLOQCTA"
    elif request_type == "Unlock Wallet":
        return "DESBLCTA"
    elif request_type == "Resume User":
        return "DESBUSR"
    elif request_type == "CHANGE_AUTH_FACTOR":
        return "CPIN"
    elif request_type == "RESET_AUTH_VALUE":
        return "RPIN"
    elif request_type == "ActivateUser":
        return "ACTIVA"
    elif request_type == "ActivateCuenta":
        return "AGRCTA"
    elif request_type == "AfiliaUser":
        return "AFILIA"
    elif request_type == "ClosedUserAccount":  
        return "CUSR"
    elif request_type == "ClosedAccount":
        return "CCUENTA"
    else:
        return None

# Función para asignar valores al campo 'campo modificado'
def map_modified_field(key):
    field_map = {
        'firstName': 'CNOMBRE',
        'lastName': 'CNOMBRE',
        'attr1': 'CTELCO',
        'mobileNumber': 'CCEL',
        'preferredLanguage': 'CIDIOMA',
        'marketingProfileId': 'CPCTA'
    }
    return field_map.get(key, key)


# Función para asignar los valores de las claves a las columnas correspondientes
def assign_to_column(row):
    # Convertir el string JSON a diccionario
    old_value_dict = json.loads(row['old_value_json']) if pd.notnull(row['old_value_json']) else {}

    # Inicializar valores por defecto
    last_name = None
    first_name = None
    msisdn = None
    telcob = None
    idiomab = None

    # Comprobar si las claves 'lastName', 'firstName', 'mobileNumber' están en el diccionario
    if 'lastName' in old_value_dict:
        last_name = old_value_dict['lastName']
    if 'firstName' in old_value_dict:
        first_name = old_value_dict['firstName']
    if 'mobileNumber' in old_value_dict:
        msisdn = old_value_dict['mobileNumber']
    if 'attr1' in old_value_dict:
        telcob = old_value_dict['attr1']
    if 'preferredLanguage' in old_value_dict:
        idiomab = old_value_dict['preferredLanguage']

    # Retornar los valores para las columnas correspondientes
    return pd.Series({
        'NAPELLIDO': last_name,
        'NNOMBRE': first_name,
        'MSISDNB': msisdn,
        'TELCOB' : telcob,
        'IDIOMAB': idiomab,
    })


# Función para crear el diccionario en formato JSON
def create_json_from_values(modified_field_str, old_value_str):
    # Separar las cadenas por comas
    fields = [field.strip() for field in modified_field_str.split(',')]
    values = [value.strip() for value in old_value_str.split(',')]
    
    # Crear el diccionario de campos: valores
    return {fields[i]: values[i] for i in range(len(fields))}


def extraer_json(row):
    # Convertir la columna JSON en un diccionario
    json_data = json.loads(row['old_value_json'])
    # Extraer las tres claves
    return json_data['authorizationProfileId'], json_data['marketingProfileId'], json_data['securityProfileId']


def assign_profile_to_column(row, conv_df):
    # Convertir el string JSON a diccionario
    old_value_dict = json.loads(row['old_value_json']) if pd.notnull(row['old_value_json']) else {}

    # Extraer claves
    authorizationProfileId = old_value_dict.get('authorizationProfileId', None)
    marketingProfileId = old_value_dict.get('marketingProfileId', None)
    securityProfileId = old_value_dict.get('securityProfileId', None)

    # Lógica para TIPOTRANSACCION == 'CPERFIL'
    if row['TIPOTRANSACCION'] == 'CPERFIL':
        # Realizamos la combinación con conv_df usando las tres claves extraídas
        if authorizationProfileId and marketingProfileId and securityProfileId:
            # Filtramos el conv_df por las tres claves
            match = conv_df[(conv_df['AUTHZ_PRO_CODE'] == authorizationProfileId) &
                             (conv_df['MKT_PRO_CODE'] == marketingProfileId) &
                             (conv_df['SEC_PRO_CODE'] == securityProfileId)]
            
            # Si encontramos una coincidencia, asignamos el valor de CATEGORY_NAME a PERFILB
            if not match.empty:
                category_name = match['CATEGORY_NAME'].values[0]
                return category_name
        return row['PERFILB']  # Si no hay coincidencia, mantenemos el valor original de PERFILB
    
    return row['PERFILB']  # Para otros tipos de transacción, mantenemos el valor original


def assign_cuentaperfilb_to_column(row, conv_df):
    # Convertir el string JSON a diccionario
    old_value_dict = json.loads(row['old_value_json']) if pd.notnull(row['old_value_json']) else {}

    # Extraer claves
    marketingProfileId = old_value_dict.get('marketingProfileId', None)

    # Lógica para TIPOTRANSACCION == 'CPCTA'
    if row['TIPOTRANSACCION'] == 'CPCTA':
        # Filtramos el conv_df solo por MKT_PRO_CODE
        if marketingProfileId:
            match = conv_df[conv_df['MKT_PRO_CODE'] == marketingProfileId]
            
            # Si encontramos una coincidencia, asignamos el valor de MARKETING_PROFILE_NAME a PERFILCUENTAB
            if not match.empty:
                marketing_profile_name = match['MARKETING_PROFILE_NAME'].values[0]
                return marketing_profile_name

    return row['PERFILCUENTAB']


def assign_cuentaperfilb_to_rows_v2(row):
    """ Crea nuevos registros dependiendo de la comparación de PERFILA y PERFILB. """
    
    new_rows = []  # Lista de registros resultantes
    
    # Comprobamos si la transacción es 'CPCTA'
    if row['TIPOTRANSACCION'] == 'CPCTA':
        perfil_a = row['PERFILCUENTAA']
        perfil_b = row['PERFILCUENTAB']
        user_a = row['USERID']
        user_b = row['USERIDOLD']
        account_a = row['ACCOUNTID']
        account_b = row['ACCOUNTIDOLD']
        documento_a = row['NUMDOCUMENTOB']
        documento_b = str(row['NUMDOCUMENTOB']) + 'X' + str(row['USERIDOLD'])
        nombre_a = row['NOMBRE']
        apellido_a = row['APELLIDO']
        
        # Verificar si los perfiles son diferentes
        if perfil_a != perfil_b:
            # Extraer la primera palabra de cada perfil
            first_word_a = perfil_a.split()[0] if perfil_a and ' ' in perfil_a else perfil_a
            first_word_b = perfil_b.split()[0] if perfil_b and ' ' in perfil_b else perfil_b
            
            # Crear tres nuevos registros con diferentes tipos de transacción
            for tipo, perfil, cuenta, user, account, documento, nombre, apellido in [
                                 ('CNOMBRE', first_word_a, perfil_a, user_a, account_a, documento_a, nombre_a, apellido_a),('CUSR', first_word_b, perfil_b, user_b, account_b, documento_b, nombre_a, apellido_a), ('CCUENTA', first_word_b, perfil_b, user_b, account_b, documento_b, nombre_a, apellido_a)]:
                new_row = row.copy()
                new_row['TIPOTRANSACCION'] = tipo
                new_row['BANKDOMAIN'] = perfil  # Usamos la primera parte de PERFILA o PERFILB
                new_row['PERFILCUENTAA'] = cuenta
                #new_row['PERFILA'] = cuenta
                new_row['PERFILCUENTA'] = cuenta
                new_row['PERFILCUENTAB'] = ''
                new_row['CREATED_BY'] = user
                new_row['USERID'] = user
                new_row['ACCOUNTID'] = account
                new_row['NUMDOCUMENTOB'] = documento
                new_row['NNOMBRE'] = nombre
                new_row['NAPELLIDO'] = apellido
                print(new_row)
                new_rows.append(new_row)   
    
    else:
        # Si no es 'CPCTA', simplemente agregamos el registro tal cual está
        new_rows.append(row.copy())
    
    return new_rows  # Devuelve la lista de registros generados

#funcion que genera los id de transaccion para los registros
def id_generator_by_date(date: str) -> str:
    date_obj = datetime.strptime(date, "%Y-%m-%d %H:%M:%S.%f")
    timestamp_actual = int(date_obj.timestamp() * (10 ** 5))
    random_numbers = secrets.randbelow(10)
    identifier = str(timestamp_actual) + str(random_numbers).zfill(1)
    return str(identifier)

def id_generator_by_reference() -> str:
    generated_ref = id_generator_by_date("2025-03-15 00:00:00.000001")
    generated_now = id_generator_by_date(datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"))
    difference = int(generated_now) - int(generated_ref)
    time.sleep(0.015)
    return str(difference)


# Función para filtrar y procesar cada tipo de transacción
def procesar_transaccion(df, tipo_transaccion):
    # Verificar si el tipo de transacción está en el diccionario
    if tipo_transaccion not in tipos_transaccion_map:
        return df  # Si el tipo no está mapeado, simplemente devolvemos el DataFrame original
    
    # Obtener las columnas relevantes para este tipo de transacción
    columnas_relevantes = tipos_transaccion_map[tipo_transaccion]

    # Filtrar el DataFrame para este tipo de transacción
    df_filtrado = df[df['TipoTransaccion'] == tipo_transaccion].copy()

    # Para las columnas no incluidas, asignar None (null)
    todas_columnas = df_filtrado.columns
    for col in todas_columnas:
        if col not in columnas_relevantes:
            df_filtrado[col] = None
    
    return df_filtrado


def main():
    #Recibir fecha de input
    input_date = sys.argv[1]

    data_day = datetime.strptime(input_date, '%Y/%m/%d') + timedelta(days=1)
    path_day = data_day.strftime('%Y-%m-%d')
    data_day = data_day.strftime('%Y%m%d')

    # Obtener la fecha y hora actual y sumar un día directamente
    current_time = (datetime.now() + timedelta(days=0)).strftime("%Y%m%d")
    current_time_complete = (datetime.now() + timedelta(days=0)).strftime("%H%M%S")

    # Cargar el archivo CSV
    input_filename = f"{input_path}/data-{data_day}.csv"
    df = pd.read_csv(input_filename, dtype=str)

    # Capturar la fecha y hora actual en el formato deseado
    #current_time = datetime.now().strftime("%Y%m%d%H%M%S")
    # Lista para almacenar las filas procesadas
    processed_rows = []

    # Agrupar por USERHISTID
    grouped_df = df.groupby('USERHISTID')

    # Procesar los datos
    for userhistid, group in grouped_df:
        # Lista para almacenar las filas del grupo procesado
        group_processed_rows = []
        
        # Procesar las filas del grupo
        for _, row in group.iterrows():
            request_type = row['REQUESTTYPE']
            
            # Manejar valores especiales según REQUESTTYPE
            special_value = handle_request_type(request_type)
            
            if special_value:
                new_row = row.to_dict()
                new_row['campo modificado'] = special_value  # Asignar valor especial
                new_row['old_value'] = None
                new_row['new_value'] = None
                new_row['modified_field'] = None
                group_processed_rows.append(new_row)
            else:
                # Procesar los JSON (OLDDATA y NEWDATA)
                old_data = json.loads(row['OLDDATA'])
                new_data = json.loads(row['NEWDATA'])
                
                # Comparar los datos
                differences = compare_data(old_data, new_data)
                
                for diff in differences:
                    new_row = row.to_dict()
                    modified_field = get_final_key(diff['campo'])
                    new_row['campo modificado'] = map_modified_field(modified_field)
                    new_row['old_value'] = diff['old_value']
                    new_row['new_value'] = diff['new_value']
                    new_row['modified_field'] = modified_field
                    group_processed_rows.append(new_row)

        # Verificar si los tres campos están presentes en 'campo modificado' del grupo
        if all(field in [row['modified_field'] for row in group_processed_rows] for field in ['authorizationProfileId', 'marketingProfileId', 'securityProfileId']):
            # Si están presentes, asignar 'CPERFIL' al campo modificado en todas las filas del grupo
            for row in group_processed_rows:
                row['campo modificado'] = 'CPERFIL'

        # Agregar las filas procesadas del grupo a la lista final
        processed_rows.extend(group_processed_rows)

    # Convertir a DataFrame
    df_processed = pd.DataFrame(processed_rows)

    # Filtrar los registros con los valores específicos en 'campo modificado'
    valid_values = [
        'AFILIA', 'ACTIVA', 'BLOQUSR', 'BLOQCTA', 'CCEL' , 'CTELCO', 
        'CPIN', 'CPCTA', 'CCUENTA', 'CPERFIL', 'CNOMBRE', 'CIDIOMA', 'CUSR',
        'DESBLCTA', 'DESBUSR', 'RPIN'
    ]
    df_processed = df_processed[df_processed['campo modificado'].isin(valid_values)]

    df_processed['old_value'] = df_processed.groupby(['USERHISTID', 'campo modificado'])['old_value'].transform(lambda x: ', '.join(x.astype(str)))
    df_processed['new_value'] = df_processed.groupby(['USERHISTID', 'campo modificado'])['new_value'].transform(lambda x: ', '.join(x.astype(str)))
    df_processed['modified_field'] = df_processed.groupby(['USERHISTID', 'campo modificado'])['modified_field'].transform(lambda x: ', '.join(x.astype(str)))

    # Aplicar la función a las columnas ya existentes y crear un JSON para 'old_value'
    df_processed['old_value_json'] = df_processed.apply(
        lambda row: json.dumps(create_json_from_values(row['modified_field'], row['old_value'])) 
        if pd.notnull(row['modified_field']) and pd.notnull(row['old_value']) else '{}',
        axis=1
    )

    # Crear un JSON similar para 'new_value' si lo necesitas también
    df_processed['new_value_json'] = df_processed.apply(
        lambda row: json.dumps(create_json_from_values(row['modified_field'], row['new_value'])) 
        if pd.notnull(row['modified_field']) and pd.notnull(row['new_value']) else '{}',
        axis=1
    )
    # Eliminar los registros duplicados basados en 'USERHISTID' y 'campo modificado'
    df_processed = df_processed.drop_duplicates(subset=['USERHISTID', 'campo modificado'])


    # Renombrar la columna 'campo modificado' a 'transactionType'
    df_processed = df_processed.rename(columns={'campo modificado': 'TIPOTRANSACCION'})
    df_processed = df_processed.rename(columns={'USERHISTID': 'TRANSACTIONID'})
    df_processed = df_processed.rename(columns={'CREATEDON': 'DIAHORA'})

    # Reordenar las columnas para que 'transactionType' esté primero
    column_order = ['TIPOTRANSACCION'] + [col for col in df_processed.columns if col != 'TIPOTRANSACCION']
    df_processed = df_processed[column_order]

    # Aplicar la función para asignar valores a las columnas correspondientes
    df_processed[['NAPELLIDO', 'NNOMBRE', 'MSISDNB', 'TELCOB', 'IDIOMAB']] = df_processed.apply(assign_to_column, axis=1)

    conv_df = pd.read_csv('conv_perfil.csv')

    df_processed['PERFILB'] = df_processed.apply(lambda row: assign_profile_to_column(row, conv_df), axis=1)
    df_processed['PERFILCUENTAB'] = df_processed.apply(lambda row: assign_cuentaperfilb_to_column(row, conv_df), axis=1)

    print(df_processed)
    #NUEVA LOGICA PARA ISSUERMOV

    # Aplicar la nueva función a cada fila del DataFrame
    expanded_rows = df_processed.apply(lambda row: assign_cuentaperfilb_to_rows_v2(row), axis=1)

    # Convertir la lista de listas en un solo DataFrame
    df_processed = pd.DataFrame([item for sublist in expanded_rows for item in sublist])

    print(df_processed)

    # Eliminar columnas OLDDATA y NEWDATA si existen
    df_processed = df_processed.drop(columns=['OLDDATA', 'NEWDATA','REQUESTTYPE','old_value','new_value','modified_field','new_value_json'], errors='ignore')

    # Definir los reemplazos de nombres de columnas
    reemplazos = {
        'TIPOTRANSACCION': 'TipoTransaccion',
        'TRANSACTIONID': 'TransactionID',
        'DIAHORA': 'DiaHora',
        'TIPOCUENTA': 'TipoCuenta',
        'TIPODOCUMENTO': 'TipoDocumento',
        'DOCUMENTO': 'Documento',
        'MSISDN': 'MSISDN',
        'BANKDOMAIN': 'BankDomain',
        'NOMBRE': 'Nombres',
        'APELLIDO': 'Apellidos',
        'PERFILA': 'PerfilA',
        'PERFILB': 'PerfilB',
        'IDIOMAA': 'IdiomaA',
        'IDIOMAB': 'IdiomaB',
        'TELCOA': 'TelcoA',
        'TELCOB': 'TelcoB',
        'RAZON': 'Razon',
        'CREATED_BY': 'Initiating User',
        'NNOMBRE': 'NNombre',
        'NAPELLIDO': 'NApellido',
        'USERID': 'ID USUARIO',
        'ACCOUNTID': 'ID CUENTA',
        'PERFILCUENTA': 'PerfilCuenta',
        'PERFILCUENTAA': 'PerfilCuentaA',
        'PERFILCUENTAB': 'PerfilCuentaB',
        'TIPODOCUMENTOA': 'TipoDocumentoA',
        'TIPODOCUMENTOB': 'TipoDocumentoB',
        'DOCUMENTOB': 'NumDocumentoA',
        'NUMDOCUMENTOB': 'NumDocumentoB'
    }

    # Aplicar los reemplazos a las columnas del DataFrame
    df_processed = df_processed.rename(columns=reemplazos)

    df_processed.to_csv('USRLOG-PREV.csv', index=False)

    # Procesar los tipos de transacciones válidas y concatenarlos en un solo DataFrame
    dfs_procesados = []

    for tipo in valid_values:
        df_tipo = procesar_transaccion(df_processed, tipo)
        dfs_procesados.append(df_tipo)

    # Concatenar todos los DataFrames procesados en uno solo
    df_final = pd.concat(dfs_procesados, ignore_index=True)

    # Convertir la columna 'DiaHora' a tipo datetime
    #df_final['DiaHora'] = pd.to_datetime(df_final['DiaHora'])

    #df_final['TransactionID'] = [
    #    f'{row.DiaHora.strftime("%Y%m%d")}2{i:06d}' for i, row in enumerate(df_final.itertuples(), 1)
    #]

    df_final['DiaHora'] = pd.to_datetime(df_final['DiaHora'])

    df_final['DiaHora'] = df_final['DiaHora'].dt.strftime('%Y-%m-%d %H:%M:%S')

    df_final['NumDocumentoA'] = df_final['NumDocumentoA'].apply(lambda x: str(x) if pd.notnull(x) and x != '' else '')

    df_final['MSISDNB'] = df_final['MSISDNB'].apply(lambda x: str(int(x)) if pd.notnull(x) else '')

    df_final['ID USUARIO'] = df_final['ID USUARIO'].apply(lambda x: str(int(x)) if pd.notnull(x) else '')

    df_final['ID CUENTA'] = df_final['ID CUENTA'].apply(lambda x: str(int(x)) if pd.notnull(x) else '')

    # Asignar el orden personalizado a la columna 'TipoTransaccion'
    df_final['TipoTransaccion'] = pd.Categorical(df_final['TipoTransaccion'], categories=valid_values, ordered=True)
    # Ordenar el DataFrame por la columna 'DiaHora'
    df_final = df_final.sort_values(by=['DiaHora','TipoTransaccion'], ascending = [True,True])

    # Definir el orden deseado de las columnas
    column_order = [
        'TipoTransaccion',
        'TransactionID',
        'DiaHora',
        'TipoCuenta',
        'TipoDocumento',
        'Documento',
        'MSISDN',
        'BankDomain',
        'Nombres',
        'Apellidos',
        'PerfilA',
        'PerfilB',
        'IdiomaA',
        'IdiomaB',
        'TelcoA',
        'TelcoB',
        'Razon',
        'Initiating User',
        'MSISDNB',
        'NNombre',
        'NApellido',
        'ID USUARIO',
        'ID CUENTA',
        'PerfilCuenta',
        'PerfilCuentaA',
        'PerfilCuentaB',
        'TipoDocumentoA',
        'TipoDocumentoB',
        'NumDocumentoA',
        'NumDocumentoB'
    ]

    # Reordenar las columnas del DataFrame df_final
    df_final = df_final[column_order]


    # Guardar el DataFrame final en un archivo CSV
    df_final.to_csv('USRLOG_v5.csv', index=False)

    # Iteramos sobre los valores únicos en la columna 'bankDomain'
    for bank_domain in df_final['BankDomain'].unique():
        # Filtrar el DataFrame para obtener solo las filas correspondientes a 'bankDomain'
        df_filtered = df_final[df_final['BankDomain'] == bank_domain]
        #bank_domain_path = re.sub(r'[^A-Za-z]', '', bank_domain)

        # Crear el nombre del archivo basado en el valor de 'bankDomain'
        file_name = f"output/LOGUSR-{bank_domain}-{data_day}{current_time_complete}.csv"
        # Guardar el DataFrame filtrado en un archivo CSV
        df_filtered.to_csv(file_name, index=False, header=False)
        print(f"Archivo generado: {file_name}")

        #data_day = datetime.now().strftime("%Y-%m-%d")
        s3_key = f"{bank_domain}/{path_day}/LOG_USUARIOS/"
        signer = FileSigner(private_key_path)
        signer.sign_file(file_name)
        signature_path = f"{file_name}.signature"
        is_valid = signer.verify_signature(file_name, signature_path, public_key_path)
        signature_file = file_name + ".signature"
        if is_valid:
            upload_s3(signature_path, signature_file, s3_key)

        # Subir el archivo a S3    
        upload_s3(file_name, os.path.basename(file_name), s3_key)

if __name__ == "__main__":
    main()
