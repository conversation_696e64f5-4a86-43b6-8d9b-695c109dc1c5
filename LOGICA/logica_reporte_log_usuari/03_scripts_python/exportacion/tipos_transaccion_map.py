tipos_transaccion_map = {
    'AFILIA': [
        'TipoTransaccion', 'TransactionID', 'DiaHora', 'TipoCuenta', 'TipoDocumento',
        'Documento', 'MSISDN', 'BankDomain', 'Nombres', 'Apellidos', 'PerfilA',
        'IdiomaA', 'TelcoA', 'Initiating User'
    ],
    'ACTIVA': [
        'TipoTransaccion', 'TransactionID', 'DiaHora', 'TipoCuenta', 'TipoDocumento',
        'Documento', 'MSISDN', 'BankDomain', 'Nombres', 'Apellidos', 'PerfilA',
        'IdiomaA', 'TelcoA', 'Initiating User'
    ],
    'BLOQUSR': [
        'TipoTransaccion', 'TransactionID', 'DiaHora', 'TipoCuenta', 'TipoDocumento',
        'Documento', 'MSISDN', 'BankDomain', 'Nombres', 'Apellidos', 'PerfilA',
        'IdiomaA', 'TelcoA', 'Razon', 'Initiating User'
    ],
    'BLOQCTA': [
        'TipoTransaccion', 'TransactionID', 'DiaHora', 'TipoCuenta', 'TipoDocumento',
        'Documento', 'MSISDN', 'BankDomain', 'Nombres', 'Apellidos', 'PerfilA',
        'IdiomaA', 'TelcoA', 'Razon', 'Initiating User', 'ID CUENTA', 'PerfilCuenta'
    ],
    'CCEL': [
        'TipoTransaccion', 'TransactionID', 'DiaHora', 'TipoCuenta', 'TipoDocumento',
        'Documento', 'MSISDN', 'BankDomain', 'Nombres', 'Apellidos', 'PerfilA',
        'IdiomaA', 'TelcoA', 'Initiating User', 'MSISDNB'
    ],
    'CTELCO': [
        'TipoTransaccion', 'TransactionID', 'DiaHora', 'TipoCuenta', 'TipoDocumento',
        'Documento', 'MSISDN', 'BankDomain', 'Nombres', 'Apellidos', 'PerfilA',
        'IdiomaA', 'TelcoA', 'Initiating User', 'TelcoB'
    ],
    'CPIN': [
        'TipoTransaccion', 'TransactionID', 'DiaHora', 'TipoCuenta', 'TipoDocumento',
        'Documento', 'MSISDN', 'BankDomain', 'Nombres', 'Apellidos', 'PerfilA',
        'IdiomaA', 'TelcoA', 'Initiating User'
    ],
    'CPCTA': [
        'TipoTransaccion', 'TransactionID', 'DiaHora', 'TipoCuenta', 'TipoDocumento',
        'Documento', 'MSISDN', 'BankDomain', 'Nombres', 'Apellidos', 'PerfilA',
        'IdiomaA', 'TelcoA', 'Initiating User', 'ID CUENTA', 'PerfilCuentaA', 'PerfilCuentaB'
    ],
    'CCUENTA': [
        'TipoTransaccion', 'TransactionID', 'DiaHora', 'TipoCuenta', 'TipoDocumento',
        'Documento', 'MSISDN', 'BankDomain', 'Nombres', 'Apellidos', 'PerfilA',
        'IdiomaA', 'TelcoA', 'Initiating User', 'ID CUENTA', 'PerfilCuenta'
    ],
    'CPERFIL': [
        'TipoTransaccion', 'TransactionID', 'DiaHora', 'TipoCuenta', 'TipoDocumento',
        'Documento', 'MSISDN', 'BankDomain', 'Nombres', 'Apellidos', 'PerfilA',
        'PerfilB', 'IdiomaA', 'TelcoA', 'Initiating User'
    ],
    'CNOMBRE': [
        'TipoTransaccion', 'TransactionID', 'DiaHora', 'TipoCuenta', 'TipoDocumento',
        'Documento', 'MSISDN', 'BankDomain', 'Nombres', 'Apellidos', 'PerfilA',
        'IdiomaA', 'TelcoA', 'Initiating User', 'NNombre', 'NApellido'
    ],
    'CIDIOMA': [
        'TipoTransaccion', 'TransactionID', 'DiaHora', 'TipoCuenta', 'TipoDocumento',
        'Documento', 'MSISDN', 'BankDomain', 'Nombres', 'Apellidos', 'PerfilA',
        'IdiomaA', 'TelcoA', 'Initiating User', 'IdiomaB'
    ],
    'CUSR': [
        'TipoTransaccion', 'TransactionID', 'DiaHora', 'TipoCuenta', 'TipoDocumento',
        'Documento', 'MSISDN', 'BankDomain', 'Nombres', 'Apellidos', 'PerfilA',
        'IdiomaA', 'TelcoA', 'Initiating User', 'ID USUARIO'
    ],
    'DESBLCTA': [
        'TipoTransaccion', 'TransactionID', 'DiaHora', 'TipoCuenta', 'TipoDocumento',
        'Documento', 'MSISDN', 'BankDomain', 'Nombres', 'Apellidos', 'PerfilA',
        'IdiomaA', 'TelcoA', 'Initiating User', 'ID CUENTA', 'PerfilCuenta'
    ],
    'DESBUSR': [
        'TipoTransaccion', 'TransactionID', 'DiaHora', 'TipoCuenta', 'TipoDocumento',
        'Documento', 'MSISDN', 'BankDomain', 'Nombres', 'Apellidos', 'PerfilA',
        'IdiomaA', 'TelcoA', 'Initiating User'
    ],
    'RPIN': [
        'TipoTransaccion', 'TransactionID', 'DiaHora', 'TipoCuenta', 'TipoDocumento',
        'Documento', 'MSISDN', 'BankDomain', 'Nombres', 'Apellidos', 'PerfilA',
        'IdiomaA', 'TelcoA', 'Initiating User'
    ]
}
