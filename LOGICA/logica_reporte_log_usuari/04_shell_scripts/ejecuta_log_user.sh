#!/bin/bash

if [ -z "$1" ]; then
    fecha=$(date -d "yesterday" +"%Y/%m/%d")
else
    fecha=$1
fi

LOG_FILE="execution_status.log"
MAX_TIME=1500
MAX_RETRIES=3

echo "$fecha"

cd /home/<USER>/generate/


check_status_and_run() {
	PART_NAME=$1
	RETRY_COUNT=0
	
	while [ "$RETRY_COUNT" -lt "$MAX_RETRIES" ]; do
		echo "$(date) - Ejecutando $PART_NAME, intento #$((RETRY_COUNT + 1))"

		start_time=$(date +%s)

		python3 prepare/main.py "$fecha" "$PART_NAME" > "logs/prepare/$PART_NAME.log"  2>&1 &
		pid=$!

		while kill -0 $pid 2>/dev/null; do
			elapsed_time=$(( $(date +%s) - start_time ))
			if [ "$elapsed_time" -gt "$MAX_TIME" ]; then
				echo "$(date) - El proceso $PART_NAME excedió el tiempo límite de $MAX_TIME segundos. Reiniciando..."
				kill -9 $pid
				break
			fi

			sleep 5

		done

		wait $pid
		if [ $? -eq 0 ]; then
			echo "$(date) - $PART_NAME completado exitosamente"
			return
		else
			echo "$(date) - $PART_NAME falló."
		fi

		RETRY_COUNT=$((RETRY_COUNT + 1))

		if [ "$RETRY_COUNT" -lt "$MAX_RETRIES" ]; then
			echo "$(date) - Reintentando $PART_NAME..."
		fi
	done

	if [ "$RETRY_COUNT" -ge "$MAX_RETRIES" ]; then
		echo "$(date) - El proceso $PART_NAME falló después de 3 intentos."
	fi

}

echo "Proceso previo iniciado."
check_status_and_run "PRE-LOG-USR"
echo "Proceso previo finalizado."

echo "Proceso LOG-USR inicidado."
check_status_and_run "LOG-USR"
echo "Proceso LOG-USR finalizado."


echo "Generando log de usuarios"

sh -x /home/<USER>/generate/log_user.sh "$fecha"

