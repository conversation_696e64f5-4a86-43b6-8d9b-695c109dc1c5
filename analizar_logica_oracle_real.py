#!/usr/bin/env python3
"""
Análisis de la lógica REAL de Oracle vs nuestro pipeline
Comparar exactamente lo que hace Oracle vs lo que estamos haciendo
"""

import pandas as pd
import duckdb
import boto3

def analyze_oracle_sp_pre_log_usr_logic():
    """Analizar la lógica exacta del SP_PRE_LOG_USR de Oracle"""
    print("=== ANÁLISIS LÓGICA ORACLE SP_PRE_LOG_USR ===")
    print("Según el código Oracle:")
    print("1. INNER JOIN PDP_PROD10_MAINDB.KYC_DETAILS UK ON UP.KYC_ID = UK.KYC_ID")
    print("2. SELECT UK.ID_TYPE, UK.ID_VALUE")
    print("3. Esto significa que TODOS los usuarios deben tener KYC_ID válido")
    print("4. Si no tienen KYC_ID, no aparecen en USER_DATA_TRX")
    print()

def test_our_pipeline_logic():
    """Probar nuestra lógica actual del pipeline"""
    try:
        session = boto3.Session()
        conn = duckdb.connect()
        conn.execute("INSTALL httpfs;")
        conn.execute("LOAD httpfs;")
        
        credentials = session.get_credentials()
        conn.execute(f"""
        SET s3_region='us-east-1';
        SET s3_access_key_id='{credentials.access_key}';
        SET s3_secret_access_key='{credentials.secret_key}';
        """)
        
        if credentials.token:
            conn.execute(f"SET s3_session_token='{credentials.token}';")
        
        print("=== PRUEBA LÓGICA NUESTRO PIPELINE ===")
        
        user_profile_path = "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/USER_PROFILE_ORA/consolidado_puro.parquet"
        kyc_details_path = "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/KYC_DETAILS_ORA/consolidado_puro.parquet"
        
        # Replicar EXACTAMENTE la lógica de Oracle
        oracle_logic_query = f"""
        WITH KYC_PRIMARY AS (
            SELECT
                KYC.KYC_ID,
                KYC.ID_TYPE,
                KYC.ID_VALUE,
                ROW_NUMBER() OVER (PARTITION BY KYC.KYC_ID ORDER BY 
                    CASE WHEN KYC.IS_PRIMARY_KYC_ID = 'Y' THEN 1 ELSE 2 END,
                    COALESCE(KYC.CREATED_ON, '1900-01-01') DESC
                ) AS ORDEN
            FROM read_parquet('{kyc_details_path}') KYC
            WHERE KYC.KYC_ID IS NOT NULL
            AND KYC.STATUS = 'A'
        )
        SELECT 
            COUNT(*) as TOTAL_USERS_WITH_KYC,
            COUNT(CASE WHEN KYC.ID_TYPE = 'DNI' THEN 1 END) as DNI_COUNT,
            COUNT(CASE WHEN KYC.ID_TYPE = 'CE' THEN 1 END) as CE_COUNT,
            COUNT(CASE WHEN KYC.ID_TYPE = 'RUC' THEN 1 END) as RUC_COUNT,
            COUNT(CASE WHEN KYC.ID_TYPE = 'OTHER' THEN 1 END) as OTHER_COUNT
        FROM read_parquet('{user_profile_path}') UP
        INNER JOIN KYC_PRIMARY KYC ON UP.KYC_ID = KYC.KYC_ID AND KYC.ORDEN = 1
        WHERE UP.CREATED_ON <= '2025-06-03'
        """
        
        df_oracle_logic = conn.execute(oracle_logic_query).df()
        print("Resultado aplicando lógica EXACTA de Oracle:")
        print(df_oracle_logic.to_string(index=False))
        
        # Verificar usuarios SIN KYC_ID
        no_kyc_query = f"""
        SELECT 
            COUNT(*) as USERS_WITHOUT_KYC_ID
        FROM read_parquet('{user_profile_path}') UP
        WHERE (UP.KYC_ID IS NULL OR UP.KYC_ID = '')
        AND UP.CREATED_ON <= '2025-06-03'
        """
        
        df_no_kyc = conn.execute(no_kyc_query).df()
        print(f"\nUsuarios SIN KYC_ID (excluidos por INNER JOIN):")
        print(df_no_kyc.to_string(index=False))
        
        return df_oracle_logic
        
    except Exception as e:
        print(f"Error probando lógica: {e}")
        return None

def analyze_our_current_result():
    """Analizar nuestro resultado actual"""
    try:
        conn = duckdb.connect()
        
        user_data_path = "S3_LOG_USER/TEMP_LOGS_USUARIOS/20250603/USER_DATA_TRX.parquet"
        
        print("\n=== ANÁLISIS NUESTRO USER_DATA_TRX ACTUAL ===")
        
        query = f"""
        SELECT 
            COUNT(*) as TOTAL_RECORDS,
            COUNT(CASE WHEN ID_TYPE = 'DNI' THEN 1 END) as DNI_COUNT,
            COUNT(CASE WHEN ID_TYPE = 'CE' THEN 1 END) as CE_COUNT,
            COUNT(CASE WHEN ID_TYPE = 'RUC' THEN 1 END) as RUC_COUNT,
            COUNT(CASE WHEN ID_TYPE = 'OTHER' THEN 1 END) as OTHER_COUNT,
            COUNT(CASE WHEN ID_TYPE IS NULL OR ID_TYPE = '' THEN 1 END) as NULL_COUNT
        FROM read_parquet('{user_data_path}')
        """
        
        df_our_result = conn.execute(query).df()
        print("Nuestro resultado actual:")
        print(df_our_result.to_string(index=False))
        
        # Verificar algunos ejemplos de ID_TYPE
        sample_query = f"""
        SELECT 
            ID_TYPE,
            COUNT(*) as CANTIDAD
        FROM read_parquet('{user_data_path}')
        GROUP BY ID_TYPE
        ORDER BY CANTIDAD DESC
        """
        
        df_sample = conn.execute(sample_query).df()
        print(f"\nDistribución ID_TYPE en nuestro resultado:")
        print(df_sample.to_string(index=False))
        
        return df_our_result
        
    except Exception as e:
        print(f"Error analizando nuestro resultado: {e}")
        return None

def compare_and_recommend():
    """Comparar y recomendar correcciones"""
    print("\n=== COMPARACIÓN Y RECOMENDACIONES ===")
    print("PROBLEMA IDENTIFICADO:")
    print("- Oracle usa INNER JOIN con KYC_DETAILS, lo que excluye usuarios sin KYC")
    print("- Nuestro pipeline puede estar incluyendo usuarios sin KYC válido")
    print("- Necesitamos replicar EXACTAMENTE la lógica de Oracle")
    print()
    print("CORRECCIÓN REQUERIDA:")
    print("1. Usar INNER JOIN con KYC_DETAILS (no LEFT JOIN)")
    print("2. Filtrar por STATUS = 'A' en KYC_DETAILS")
    print("3. Usar IS_PRIMARY_KYC_ID = 'Y' para seleccionar el KYC principal")
    print("4. Verificar que los tipos de documento coincidan exactamente")

def main():
    """Función principal"""
    print("Análisis de lógica Oracle vs Pipeline")
    print("="*60)
    
    # Analizar lógica Oracle
    analyze_oracle_sp_pre_log_usr_logic()
    
    # Probar lógica Oracle en S3
    test_our_pipeline_logic()
    
    # Analizar nuestro resultado
    analyze_our_current_result()
    
    # Comparar y recomendar
    compare_and_recommend()

if __name__ == "__main__":
    main()
