#!/usr/bin/env python3
"""
Análisis exhaustivo del flujo original Oracle - Data Engineering
Mapeo completo desde tablas origen hasta LOG_USR final
"""
import oracledb
import duckdb
import boto3

def main():
    try:
        # Conexión a Oracle
        oracle_conn = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        oracle_cursor = oracle_conn.cursor()
        
        print("=== ANÁLISIS EXHAUSTIVO FLUJO ORACLE - DATA ENGINEERING ===")
        print("Mapeo completo: Tablas Origen → SP_LOG_USR → LOG_USR")
        print("=" * 80)
        
        # 1. ANÁLISIS USER_PROFILE - Origen de datos de usuario
        print("\n1. USER_PROFILE - Datos de usuario origen:")
        print("-" * 60)
        
        oracle_cursor.execute("""
            SELECT 
                USER_ID,
                FIRST_NAME,
                LAST_NAME,
                MSISDN,
                PROFILE,
                PREFERRED_LANG,
                CREATED_ON,
                CREATED_BY
            FROM PDP_PROD10_MAINDB.USER_PROFILE
            WHERE TRUNC(CREATED_ON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            ORDER BY CREATED_ON
            FETCH FIRST 5 ROWS ONLY
        """)
        user_profile_sample = oracle_cursor.fetchall()
        
        print("USER_PROFILE muestra (2025-06-03):")
        print("USER_ID | FIRST_NAME | LAST_NAME | MSISDN | PROFILE | LANG | CREATED_BY")
        for row in user_profile_sample:
            print(f"{row[0]} | {row[1]} | {row[2]} | {row[3]} | {row[4]} | {row[5]} | {row[7]}")
        
        # 2. ANÁLISIS MTX_WALLET - Origen de BANKDOMAIN
        print("\n2. MTX_WALLET + ISSUER_DETAILS - Origen BANKDOMAIN:")
        print("-" * 60)
        
        oracle_cursor.execute("""
            SELECT 
                MW.USER_ID,
                MW.WALLET_NUMBER,
                MW.ISSUER_ID,
                ID.ISSUER_CODE,
                MW.GRADE_ID
            FROM PDP_PROD10_MAINDB.MTX_WALLET MW
            INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON MW.ISSUER_ID = ID.ISSUER_ID
            INNER JOIN PDP_PROD10_MAINDB.USER_PROFILE UP ON MW.USER_ID = UP.USER_ID
            WHERE TRUNC(UP.CREATED_ON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            ORDER BY MW.USER_ID
            FETCH FIRST 5 ROWS ONLY
        """)
        wallet_sample = oracle_cursor.fetchall()
        
        print("MTX_WALLET + ISSUER_DETAILS muestra:")
        print("USER_ID | WALLET_NUMBER | ISSUER_ID | ISSUER_CODE | GRADE_ID")
        for row in wallet_sample:
            print(f"{row[0]} | {row[1]} | {row[2]} | {row[3]} | {row[4]}")
        
        # 3. ANÁLISIS USER_MODIFICATION_HISTORY
        print("\n3. USER_MODIFICATION_HISTORY - Origen modificaciones:")
        print("-" * 60)
        
        oracle_cursor.execute("""
            SELECT 
                USER_ID,
                REQUEST_TYPE,
                CREATED_ON,
                CREATED_BY,
                OLD_DATA,
                NEW_DATA,
                RAZON
            FROM PDP_PROD10_MAINDB.USER_MODIFICATION_HISTORY
            WHERE TRUNC(CREATED_ON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            ORDER BY CREATED_ON
            FETCH FIRST 5 ROWS ONLY
        """)
        mod_sample = oracle_cursor.fetchall()
        
        print("USER_MODIFICATION_HISTORY muestra:")
        print("USER_ID | REQUEST_TYPE | CREATED_BY | RAZON")
        for row in mod_sample:
            print(f"{row[0]} | {row[1]} | {row[3]} | {row[6]}")
        
        # 4. ANÁLISIS USER_AUTH_CHANGE_HISTORY
        print("\n4. USER_AUTH_CHANGE_HISTORY - Origen cambios PIN:")
        print("-" * 60)
        
        oracle_cursor.execute("""
            SELECT 
                USER_ID,
                AUTHENTICATION_ID,
                AUTHENTICATION_TYPE,
                MODIFICATION_TYPE,
                MODIFIED_ON,
                MODIFIED_BY
            FROM PDP_PROD10_MAINDB.USER_AUTH_CHANGE_HISTORY
            WHERE TRUNC(MODIFIED_ON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            AND AUTHENTICATION_TYPE = 'PIN'
            ORDER BY MODIFIED_ON
            FETCH FIRST 5 ROWS ONLY
        """)
        auth_sample = oracle_cursor.fetchall()
        
        print("USER_AUTH_CHANGE_HISTORY muestra:")
        print("USER_ID | AUTH_ID | AUTH_TYPE | MOD_TYPE | MODIFIED_BY")
        for row in auth_sample:
            print(f"{row[0]} | {row[1]} | {row[2]} | {row[3]} | {row[5]}")
        
        # 5. ANÁLISIS CÓMO ORACLE GENERA TIPODOCUMENTO
        print("\n5. ANÁLISIS TIPODOCUMENTO EN ORACLE LOG_USR:")
        print("-" * 60)
        
        oracle_cursor.execute("""
            SELECT DISTINCT TIPODOCUMENTO, COUNT(*) as cantidad
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            GROUP BY TIPODOCUMENTO
            ORDER BY COUNT(*) DESC
        """)
        tipodoc_oracle = oracle_cursor.fetchall()
        
        print("TIPODOCUMENTO en Oracle LOG_USR:")
        for row in tipodoc_oracle:
            print(f"   '{row[0]}': {row[1]:,} registros")
        
        # 6. ANÁLISIS CÓMO ORACLE GENERA DOCUMENTO
        print("\n6. ANÁLISIS DOCUMENTO EN ORACLE LOG_USR:")
        print("-" * 60)
        
        oracle_cursor.execute("""
            SELECT 
                LENGTH(DOCUMENTO) as longitud,
                COUNT(*) as cantidad,
                MIN(DOCUMENTO) as min_doc,
                MAX(DOCUMENTO) as max_doc
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            AND DOCUMENTO IS NOT NULL
            GROUP BY LENGTH(DOCUMENTO)
            ORDER BY COUNT(*) DESC
        """)
        documento_oracle = oracle_cursor.fetchall()
        
        print("DOCUMENTO en Oracle LOG_USR (por longitud):")
        for row in documento_oracle:
            print(f"   {row[0]} dígitos: {row[1]:,} registros (ej: {row[2]} - {row[3]})")
        
        # 7. ANÁLISIS NOMBRES EN ORACLE LOG_USR
        print("\n7. ANÁLISIS NOMBRES EN ORACLE LOG_USR:")
        print("-" * 60)
        
        oracle_cursor.execute("""
            SELECT 
                CASE WHEN NOMBRE IS NULL OR NOMBRE = '' THEN '[EMPTY]' ELSE 'REAL_NAME' END as tipo_nombre,
                COUNT(*) as cantidad
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            GROUP BY CASE WHEN NOMBRE IS NULL OR NOMBRE = '' THEN '[EMPTY]' ELSE 'REAL_NAME' END
            ORDER BY COUNT(*) DESC
        """)
        nombres_oracle = oracle_cursor.fetchall()
        
        print("NOMBRES en Oracle LOG_USR:")
        for row in nombres_oracle:
            print(f"   {row[0]}: {row[1]:,} registros")
        
        # Muestra de nombres reales
        oracle_cursor.execute("""
            SELECT DISTINCT NOMBRE
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            AND NOMBRE IS NOT NULL AND NOMBRE != ''
            FETCH FIRST 10 ROWS ONLY
        """)
        nombres_sample = oracle_cursor.fetchall()
        
        print("   Ejemplos de nombres reales:")
        for row in nombres_sample:
            print(f"     - {row[0]}")
        
        # 8. ANÁLISIS MSISDN EN ORACLE LOG_USR
        print("\n8. ANÁLISIS MSISDN EN ORACLE LOG_USR:")
        print("-" * 60)
        
        oracle_cursor.execute("""
            SELECT 
                SUBSTR(MSISDN, 1, 2) as prefijo,
                LENGTH(MSISDN) as longitud,
                COUNT(*) as cantidad,
                MIN(MSISDN) as ejemplo
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            AND MSISDN IS NOT NULL
            GROUP BY SUBSTR(MSISDN, 1, 2), LENGTH(MSISDN)
            ORDER BY COUNT(*) DESC
            FETCH FIRST 5 ROWS ONLY
        """)
        msisdn_oracle = oracle_cursor.fetchall()
        
        print("MSISDN en Oracle LOG_USR:")
        for row in msisdn_oracle:
            print(f"   {row[0]}xxxxxxx ({row[1]} dígitos): {row[2]:,} registros (ej: {row[3]})")
        
        # 9. ANÁLISIS USERID EN ORACLE LOG_USR
        print("\n9. ANÁLISIS USERID EN ORACLE LOG_USR:")
        print("-" * 60)
        
        oracle_cursor.execute("""
            SELECT 
                SUBSTR(USERID, 1, 3) as prefijo,
                LENGTH(USERID) as longitud,
                COUNT(*) as cantidad,
                MIN(USERID) as ejemplo
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            AND USERID IS NOT NULL
            GROUP BY SUBSTR(USERID, 1, 3), LENGTH(USERID)
            ORDER BY COUNT(*) DESC
            FETCH FIRST 5 ROWS ONLY
        """)
        userid_oracle = oracle_cursor.fetchall()
        
        print("USERID en Oracle LOG_USR:")
        for row in userid_oracle:
            print(f"   {row[0]}xxx ({row[1]} chars): {row[2]:,} registros (ej: {row[3]})")
        
        # 10. ANÁLISIS ACCOUNTID EN ORACLE LOG_USR
        print("\n10. ANÁLISIS ACCOUNTID EN ORACLE LOG_USR:")
        print("-" * 60)
        
        oracle_cursor.execute("""
            SELECT 
                SUBSTR(ACCOUNTID, 1, 3) as prefijo,
                LENGTH(ACCOUNTID) as longitud,
                COUNT(*) as cantidad,
                MIN(ACCOUNTID) as ejemplo
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            AND ACCOUNTID IS NOT NULL
            GROUP BY SUBSTR(ACCOUNTID, 1, 3), LENGTH(ACCOUNTID)
            ORDER BY COUNT(*) DESC
            FETCH FIRST 5 ROWS ONLY
        """)
        accountid_oracle = oracle_cursor.fetchall()
        
        print("ACCOUNTID en Oracle LOG_USR:")
        for row in accountid_oracle:
            print(f"   {row[0]}xxx ({row[1]} chars): {row[2]:,} registros (ej: {row[3]})")
        
        # 11. ANÁLISIS COMPLETO DE UNA MUESTRA
        print("\n11. MUESTRA COMPLETA ORACLE LOG_USR:")
        print("-" * 60)
        
        oracle_cursor.execute("""
            SELECT 
                USERHISTID,
                TIPODOCUMENTO,
                DOCUMENTO,
                MSISDN,
                BANKDOMAIN,
                USERID,
                ACCOUNTID,
                NOMBRE,
                APELLIDO,
                REQUESTTYPE
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            ORDER BY CREATEDON
            FETCH FIRST 3 ROWS ONLY
        """)
        complete_sample = oracle_cursor.fetchall()
        
        print("Muestra completa Oracle LOG_USR:")
        print("USERHISTID | TIPODOC | DOCUMENTO | MSISDN | BANKDOMAIN | USERID | ACCOUNTID | NOMBRE | APELLIDO | REQUESTTYPE")
        print("-" * 120)
        for row in complete_sample:
            print(f"{row[0]:<12} | {row[1]:<7} | {row[2]:<9} | {row[3]:<11} | {row[4]:<10} | {row[5]:<15} | {row[6]:<15} | {row[7]:<8} | {row[8]:<8} | {row[9]}")
        
        # 12. MAPEO DE TRANSFORMACIONES
        print("\n12. MAPEO DE TRANSFORMACIONES ORACLE:")
        print("=" * 80)
        
        print("TRANSFORMACIONES IDENTIFICADAS:")
        print("1. TIPODOCUMENTO: Siempre 'DNI' (100% de registros)")
        print("2. DOCUMENTO: Números de 8 dígitos (DNI peruano)")
        print("3. MSISDN: Números de teléfono reales (11-12 dígitos)")
        print("4. BANKDOMAIN: ISSUER_CODE de ISSUER_DETAILS")
        print("5. USERID: IDs reales de USER_PROFILE")
        print("6. ACCOUNTID: WALLET_NUMBER de MTX_WALLET")
        print("7. NOMBRE/APELLIDO: Nombres reales de USER_PROFILE")
        print("8. USERHISTID: Prefijos AU./UM./US. + números secuenciales")
        
        print("\n13. RECOMENDACIONES PARA CORRECCIÓN:")
        print("=" * 80)
        print("1. Usar datos reales de USER_PROFILE para NOMBRE/APELLIDO")
        print("2. Usar WALLET_NUMBER real para ACCOUNTID")
        print("3. Generar DNI válidos de 8 dígitos para DOCUMENTO")
        print("4. Usar MSISDN real de USER_PROFILE")
        print("5. Mapear correctamente ISSUER_CODE para BANKDOMAIN")
        print("6. Usar USER_ID real para USERID")
        print("7. Forzar TIPODOCUMENTO = 'DNI'")
        print("8. Generar USERHISTID con patrones correctos")
        
        oracle_cursor.close()
        oracle_conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
