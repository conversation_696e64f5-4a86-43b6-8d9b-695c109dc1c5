#!/usr/bin/env python3
import oracledb
import sys

def main():
    try:
        # Configuración de conexión a Oracle
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        print("=== VALIDACIÓN EN ORACLE - USR_DATALAKE.LOG_USR ===")
        print("Fecha: 2025-06-03")
        print("=" * 60)
        
        # Crear cursor
        cursor = connection.cursor()
        
        # 1. Conteo total
        print('\n1. CONTEO TOTAL:')
        query1 = """
            SELECT COUNT(*) as total_registros
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
        """
        cursor.execute(query1)
        result1 = cursor.fetchone()
        print(f'   Total registros: {result1[0]:,}')
        
        # 2. Distribución por BankDomain
        print('\n2. DISTRIBUCIÓN POR BANKDOMAIN:')
        query2 = """
            SELECT BankDomain, COUNT(*) as cantidad
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            GROUP BY BankDomain
            ORDER BY COUNT(*) DESC
        """
        cursor.execute(query2)
        results2 = cursor.fetchall()
        for row in results2:
            print(f'   {row[0]}: {row[1]:,} registros')
        
        # 3. Distribución por RequestType
        print('\n3. DISTRIBUCIÓN POR REQUEST TYPE:')
        query3 = """
            SELECT requestType, COUNT(*) as cantidad
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            GROUP BY requestType
            ORDER BY COUNT(*) DESC
        """
        cursor.execute(query3)
        results3 = cursor.fetchall()
        for row in results3:
            print(f'   {row[0]}: {row[1]:,} registros')
        
        # 4. Muestra de registros
        print('\n4. MUESTRA DE REGISTROS (primeros 5):')
        query4 = """
            SELECT userHistId, createdOn, TipoDocumento, Documento, Msisdn, BankDomain, requestType
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            ORDER BY createdOn
            FETCH FIRST 5 ROWS ONLY
        """
        cursor.execute(query4)
        results4 = cursor.fetchall()
        for i, row in enumerate(results4, 1):
            print(f'   {i}. ID: {row[0]}, Fecha: {row[1]}, Banco: {row[5]}, Tipo: {row[6]}')
        
        # 5. Verificación de fechas
        print('\n5. VERIFICACIÓN DE FECHAS:')
        query5 = """
            SELECT 
                MIN(CREATEDON) as fecha_minima,
                MAX(CREATEDON) as fecha_maxima,
                COUNT(DISTINCT TRUNC(CREATEDON)) as dias_distintos
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
        """
        cursor.execute(query5)
        result5 = cursor.fetchone()
        print(f'   Fecha mínima: {result5[0]}')
        print(f'   Fecha máxima: {result5[1]}')
        print(f'   Días distintos: {result5[2]}')
        
        # 6. Verificar si existen datos para esa fecha
        print('\n6. VERIFICACIÓN DE EXISTENCIA DE DATOS:')
        query6 = """
            SELECT COUNT(*) as total_tabla
            FROM USR_DATALAKE.LOG_USR
        """
        cursor.execute(query6)
        result6 = cursor.fetchone()
        print(f'   Total registros en toda la tabla: {result6[0]:,}')
        
        # 7. Fechas disponibles cercanas
        print('\n7. FECHAS DISPONIBLES CERCANAS A 2025-06-03:')
        query7 = """
            SELECT TRUNC(CREATEDON) as fecha, COUNT(*) as registros
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) BETWEEN TO_DATE('2025-06-01', 'YYYY-MM-DD') 
                                      AND TO_DATE('2025-06-05', 'YYYY-MM-DD')
            GROUP BY TRUNC(CREATEDON)
            ORDER BY TRUNC(CREATEDON)
        """
        cursor.execute(query7)
        results7 = cursor.fetchall()
        if results7:
            for row in results7:
                print(f'   {row[0]}: {row[1]:,} registros')
        else:
            print('   No hay datos en el rango 2025-06-01 a 2025-06-05')
        
        # Cerrar conexión
        cursor.close()
        connection.close()
        
        print('\n' + '=' * 60)
        print('VALIDACIÓN ORACLE COMPLETADA')
        
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
