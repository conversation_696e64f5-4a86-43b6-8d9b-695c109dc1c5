#!/usr/bin/env python3
import oracledb
import duckdb
import boto3

def main():
    try:
        # Conexión a Oracle
        oracle_conn = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        oracle_cursor = oracle_conn.cursor()
        
        # Configurar DuckDB con S3
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        duck_conn = duckdb.connect()
        duck_conn.sql('INSTALL httpfs;')
        duck_conn.sql('LOAD httpfs;')
        duck_conn.sql('SET s3_region=\'us-east-1\';')
        duck_conn.sql('SET s3_use_ssl=true;')
        duck_conn.sql('SET s3_url_style=\'path\';')
        duck_conn.sql(f'SET s3_access_key_id=\'{credentials.access_key}\';')
        duck_conn.sql(f'SET s3_secret_access_key=\'{credentials.secret_key}\';')
        if credentials.token:
            duck_conn.sql(f'SET s3_session_token=\'{credentials.token}\';')
        
        print("=== ANÁLISIS DE DIFERENCIAS ORACLE vs S3 ===")
        print("Fecha: 2025-06-03")
        print("=" * 60)
        
        # 1. Comparar REQUEST_TYPE en Oracle vs nuestro pipeline
        print("\n1. REQUEST_TYPE en Oracle:")
        oracle_cursor.execute("""
            SELECT requestType, COUNT(*) as cantidad
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            GROUP BY requestType
            ORDER BY COUNT(*) DESC
        """)
        oracle_types = oracle_cursor.fetchall()
        for row in oracle_types:
            print(f"   {row[0]}: {row[1]:,}")
        
        print("\n2. REQUEST_TYPE en nuestro pipeline:")
        try:
            result = duck_conn.sql('''
                SELECT requestType, COUNT(*) as cantidad
                FROM read_parquet("output/20250603/LOG_USR.parquet")
                WHERE CAST(createdOn AS DATE) = CAST("2025-06-03" AS DATE)
                GROUP BY requestType
                ORDER BY cantidad DESC
            ''')
            our_types = result.fetchall()
            for row in our_types:
                print(f"   {row[0]}: {row[1]:,}")
        except Exception as e:
            print(f"   Error: {e}")
        
        # 3. Verificar datos origen en S3
        print("\n3. DATOS ORIGEN EN S3:")
        
        # USER_MODIFICATION_HISTORY en S3
        try:
            result = duck_conn.sql('''
                SELECT COUNT(*) as total
                FROM read_parquet("s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/USER_MODIFICATION_HISTORY_ORA/consolidado_puro.parquet")
                WHERE CAST(CREATED_ON AS DATE) = CAST("2025-06-03" AS DATE)
            ''')
            s3_mod = result.fetchone()[0]
            print(f"   USER_MODIFICATION_HISTORY S3: {s3_mod:,}")
        except Exception as e:
            print(f"   USER_MODIFICATION_HISTORY S3: Error - {e}")
        
        # USER_AUTH_CHANGE_HISTORY en S3
        try:
            result = duck_conn.sql('''
                SELECT COUNT(*) as total
                FROM read_parquet("s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/USER_AUTH_CHANGE_HISTORY_ORA/consolidado_puro.parquet")
                WHERE CAST(MODIFIED_ON AS DATE) = CAST("2025-06-03" AS DATE)
            ''')
            s3_auth = result.fetchone()[0]
            print(f"   USER_AUTH_CHANGE_HISTORY S3: {s3_auth:,}")
        except Exception as e:
            print(f"   USER_AUTH_CHANGE_HISTORY S3: Error - {e}")
        
        # 4. Analizar qué tipos faltan
        print("\n4. ANÁLISIS DE FALTANTES:")
        oracle_set = set([row[0] for row in oracle_types])
        our_set = set([row[0] for row in our_types])
        
        missing_in_ours = oracle_set - our_set
        extra_in_ours = our_set - oracle_set
        
        if missing_in_ours:
            print("   Tipos que faltan en nuestro pipeline:")
            for tipo in missing_in_ours:
                oracle_count = next(row[1] for row in oracle_types if row[0] == tipo)
                print(f"     - {tipo}: {oracle_count:,} registros")
        
        if extra_in_ours:
            print("   Tipos extra en nuestro pipeline:")
            for tipo in extra_in_ours:
                our_count = next(row[1] for row in our_types if row[0] == tipo)
                print(f"     - {tipo}: {our_count:,} registros")
        
        # 5. Verificar estructura de columnas
        print("\n5. VERIFICACIÓN DE TIPOS DE DATOS:")
        print("   Oracle LOG_USR tiene 34 columnas")
        
        try:
            result = duck_conn.sql('DESCRIBE SELECT * FROM read_parquet("output/20250603/LOG_USR.parquet") LIMIT 1')
            our_columns = result.fetchall()
            print(f"   Nuestro archivo tiene {len(our_columns)} columnas")
            
            if len(our_columns) != 34:
                print("   ⚠️  DIFERENCIA EN NÚMERO DE COLUMNAS")
        except Exception as e:
            print(f"   Error verificando columnas: {e}")
        
        oracle_cursor.close()
        oracle_conn.close()
        duck_conn.close()
        
        print("\n" + "=" * 60)
        print("ANÁLISIS COMPLETADO")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
