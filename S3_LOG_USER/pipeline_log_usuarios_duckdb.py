#!/usr/bin/env python3
"""
Pipeline ETL LOG_USUARIOS Modernizado - DuckDB Único
Migración completa de Oracle a Parquet usando solo DuckDB
Autor: Ingeniero de Datos
Fecha: 2025-06-05
"""

import duckdb
import boto3
import sys
import os
from datetime import datetime, timedelta
from pathlib import Path
import logging

class LogUsuariosPipeline:
    def __init__(self):
        self.setup_logging()
        self.conn = duckdb.connect()
        self.setup_s3_credentials()
        self.setup_directories()
        
        # Configuración de rutas S3
        self.s3_bucket = "prd-datalake-silver-zone-637423440311"
        self.s3_sources = {
            'user_profile': f's3://{self.s3_bucket}/PDP_PROD10_MAINDB/USER_PROFILE_ORA/consolidado_puro.parquet',
            'user_identifier': f's3://{self.s3_bucket}/PDP_PROD10_MAINDB/USER_IDENTIFIER_ORA/consolidado_puro.parquet',
            'kyc_details': f's3://{self.s3_bucket}/PDP_PROD10_MAINDB/KYC_DETAILS_ORA/consolidado_puro.parquet',
            'issuer_details': f's3://{self.s3_bucket}/PDP_PROD10_MAINDB/ISSUER_DETAILS_ORA/consolidado_puro.parquet',
            'mtx_categories': f's3://{self.s3_bucket}/PDP_PROD10_MAINDB/MTX_CATEGORIES_ORA/consolidado_puro.parquet',
            'channel_grades': f's3://{self.s3_bucket}/PDP_PROD10_MAINDB/CHANNEL_GRADES_ORA/consolidado_puro.parquet',
            'user_modification_history': f's3://{self.s3_bucket}/PDP_PROD10_MAINDB/USER_MODIFICATION_HISTORY_ORA/consolidado_puro.parquet',
            'user_auth_change_history': f's3://{self.s3_bucket}/PDP_PROD10_MAINDB/USER_AUTH_CHANGE_HISTORY_ORA/consolidado_puro.parquet',
            'mtx_wallet': f's3://{self.s3_bucket}/PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet',
            'user_account_history': f's3://{self.s3_bucket}/APP_BIM_PROD_1/USER_ACCOUNT_HISTORY/consolidado_puro.parquet'
        }
    
    def setup_logging(self):
        """Configura el sistema de logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('pipeline_log_usuarios.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('LogUsuariosPipeline')
    
    def setup_s3_credentials(self):
        """Configura credenciales S3 en DuckDB"""
        try:
            self.logger.info("Configurando credenciales S3...")
            
            # Obtener credenciales activas de AWS CLI
            session = boto3.Session()
            credentials = session.get_credentials().get_frozen_credentials()
            
            # Cargar soporte para S3 en DuckDB
            self.conn.sql("INSTALL httpfs;")
            self.conn.sql("LOAD httpfs;")
            self.conn.sql("SET s3_region='us-east-1';")
            self.conn.sql("SET s3_use_ssl=true;")
            self.conn.sql("SET s3_url_style='path';")
            
            # Pasar credenciales explícitamente
            self.conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
            self.conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
            if credentials.token:
                self.conn.sql(f"SET s3_session_token='{credentials.token}';")
            
            self.logger.info("Credenciales S3 configuradas exitosamente")
            
        except Exception as e:
            self.logger.error(f"Error configurando credenciales S3: {e}")
            raise
    
    def setup_directories(self):
        """Crea directorios necesarios"""
        directories = ['TEMP_LOGS_USUARIOS', 'output', 'logs']
        for directory in directories:
            Path(directory).mkdir(exist_ok=True)
    
    def log_execution_status(self, part: str, status: str):
        """Registra el estado de ejecución"""
        with open("execution_status.log", "a") as f:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            f.write(f"{timestamp} - {part}: {status}\n")
        self.logger.info(f"{part}: {status}")
    
    def extract_user_account_history(self, fecha: str, date_folder: str) -> str:
        """
        Extrae y filtra USER_ACCOUNT_HISTORY desde S3
        Equivalente a mysql_extract.py
        """
        try:
            self.logger.info(f"Extrayendo USER_ACCOUNT_HISTORY para fecha: {fecha}")
            
            # Query para filtrar datos por fecha
            query = f"""
            SELECT 
                USER_ID,
                ACCOUNT_ID,
                CREATED_AT,
                ATTR7_OLD,
                ATTR8_OLD,
                CATEGORY_OLD,
                ISSUER_OLD,
                GRADE_OLD
            FROM read_parquet('{self.s3_sources['user_account_history']}')
            WHERE CAST(CREATED_AT AS DATE) >= CAST('{fecha}' AS DATE)
            """
            
            # Guardar resultado temporal
            output_path = f"TEMP_LOGS_USUARIOS/{date_folder}/USER_ACCOUNT_HISTORY.parquet"
            Path(f"TEMP_LOGS_USUARIOS/{date_folder}").mkdir(parents=True, exist_ok=True)
            
            copy_query = f"""
            COPY ({query}) TO '{output_path}' (FORMAT PARQUET);
            """
            self.conn.execute(copy_query)
            
            # Verificar registros
            count_result = self.conn.execute(f"SELECT COUNT(*) FROM read_parquet('{output_path}')").fetchone()
            record_count = count_result[0] if count_result else 0
            
            self.logger.info(f"USER_ACCOUNT_HISTORY extraído: {record_count} registros -> {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"Error extrayendo USER_ACCOUNT_HISTORY: {e}")
            raise
    
    def process_sp_pre_log_usr(self, fecha: str, date_folder: str) -> str:
        """
        Procesa SP_PRE_LOG_USR usando DuckDB
        Crea USER_DATA_TRX.parquet con datos consolidados
        """
        try:
            self.logger.info(f"Ejecutando SP_PRE_LOG_USR para fecha: {fecha}")
            
            # Query SQL adaptada a la estructura real de las tablas S3
            # Usar solo columnas que realmente existen
            query = f"""
            WITH WALLETS AS (
                SELECT
                    MW.USER_ID,
                    COALESCE(MW.WALLET_NUMBER, 'N/A') AS WALLET_NUMBER,
                    COALESCE(MW.ISSUER_ID, 0) AS ISSUER_ID,
                    COALESCE(MW.USER_GRADE, 'DEFAULT') AS USER_GRADE,
                    ROW_NUMBER() OVER (PARTITION BY MW.USER_ID ORDER BY COALESCE(MW.MODIFIED_ON, MW.CREATED_ON, '1900-01-01') DESC) AS ORDEN
                FROM read_parquet('{self.s3_sources['mtx_wallet']}') MW
                WHERE MW.USER_ID IS NOT NULL
            )
            SELECT
                UP.USER_ID AS O_USER_ID,
                UP.USER_ID AS USER_ID_M,
                COALESCE(UP.FIRST_NAME, UP.USER_CODE, 'N/A') AS FIRST_NAME,
                COALESCE(UP.LAST_NAME, UP.USER_CODE, 'N/A') AS LAST_NAME,
                COALESCE(UP.MSISDN, '') AS MSISDN,
                UK.ID_TYPE,
                UK.ID_VALUE,
                COALESCE(MW.WALLET_NUMBER, '') AS WALLET_NUMBER,
                COALESCE(ID.ISSUER_CODE, 'DEFAULT') AS ISSUER_CODE,
                COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID, 'DEFAULT') AS PROFILE,
                COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID, 'DEFAULT') AS PROFILE_TRX,
                COALESCE(CG.GRADE_NAME, MW.USER_GRADE, 'DEFAULT') AS GRADE_NAME,
                COALESCE(UP.PREFERRED_LANG, 'ES') AS PREFERRED_LANG,
                COALESCE(UP.ATTR1, '') AS ATTR1,
                COALESCE(UP.CREATED_ON, '1900-01-01') AS CREATED_ON,
                COALESCE(UP.CREATED_BY, 'SYSTEM') AS CREATED_BY,
                UP.STATUS_CHANGE_ON,
                COALESCE(UP.REMARKS, '') AS REMARKS,
                COALESCE(UP.USER_CODE, UP.USER_ID) AS USER_CODE,
                COALESCE(UP.LOGIN_ID, UP.USER_ID) AS LOGIN_ID,
                COALESCE(CAST(UP.WORKSPACE_ID AS VARCHAR), '1') AS WORKSPACE_ID
            FROM read_parquet('{self.s3_sources['user_profile']}') UP
            INNER JOIN read_parquet('{self.s3_sources['kyc_details']}') UK ON UP.KYC_ID = UK.KYC_ID
            LEFT JOIN WALLETS MW ON UP.USER_ID = MW.USER_ID AND MW.ORDEN = 1
            LEFT JOIN read_parquet('{self.s3_sources['issuer_details']}') ID ON MW.ISSUER_ID = ID.ISSUER_ID
            INNER JOIN read_parquet('{self.s3_sources['mtx_categories']}') MC ON UP.CATEGORY_ID = MC.CATEGORY_CODE
            LEFT JOIN read_parquet('{self.s3_sources['channel_grades']}') CG ON CAST(MW.USER_GRADE AS VARCHAR) = CAST(CG.GRADE_CODE AS VARCHAR)
            WHERE UP.USER_ID IS NOT NULL
            AND (UP.CREATED_ON IS NULL OR CAST(UP.CREATED_ON AS DATE) <= CAST('{fecha}' AS DATE))
            """
            
            # Guardar resultado temporal
            output_path = f"TEMP_LOGS_USUARIOS/{date_folder}/USER_DATA_TRX.parquet"
            
            copy_query = f"""
            COPY ({query}) TO '{output_path}' (FORMAT PARQUET);
            """
            self.conn.execute(copy_query)
            
            # Verificar registros
            count_result = self.conn.execute(f"SELECT COUNT(*) FROM read_parquet('{output_path}')").fetchone()
            record_count = count_result[0] if count_result else 0
            
            self.logger.info(f"SP_PRE_LOG_USR completado: {record_count} registros -> {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"Error en SP_PRE_LOG_USR: {e}")
            raise
    
    def process_sp_user_modification(self, fecha: str, date_folder: str) -> str:
        """
        Procesa SP_USER_MODIFICATION usando DuckDB
        Crea USER_MODIFICATION_DAY.parquet
        """
        try:
            self.logger.info(f"Ejecutando SP_USER_MODIFICATION para fecha: {fecha}")
            
            # Query SQL equivalente al SP_USER_MODIFICATION original
            query = f"""
            SELECT 
                umh.REQUEST_TYPE,
                umh.old_data, 
                umh.new_data,
                json_extract_string(umh.new_data, '$.profileDetails.remarks') AS razon,
                umh.user_id,
                umh.created_by,
                umh.created_on
            FROM read_parquet('{self.s3_sources['user_modification_history']}') umh
            WHERE CAST(umh.CREATED_ON AS DATE) = CAST('{fecha}' AS DATE)
            """
            
            # Guardar resultado temporal
            output_path = f"TEMP_LOGS_USUARIOS/{date_folder}/USER_MODIFICATION_DAY.parquet"
            
            copy_query = f"""
            COPY ({query}) TO '{output_path}' (FORMAT PARQUET);
            """
            self.conn.execute(copy_query)
            
            # Verificar registros
            count_result = self.conn.execute(f"SELECT COUNT(*) FROM read_parquet('{output_path}')").fetchone()
            record_count = count_result[0] if count_result else 0
            
            self.logger.info(f"SP_USER_MODIFICATION completado: {record_count} registros -> {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"Error en SP_USER_MODIFICATION: {e}")
            raise
    
    def process_sp_user_auth_day(self, fecha: str, date_folder: str) -> str:
        """
        Procesa SP_USER_AUTH_DAY usando DuckDB
        Crea USER_AUTH_CHANGE_HISTORY.parquet
        """
        try:
            self.logger.info(f"Ejecutando SP_USER_AUTH_DAY para fecha: {fecha}")
            
            # Query SQL equivalente al SP_USER_AUTH_DAY original
            query = f"""
            SELECT 
                uach.MODIFIED_ON,
                uach.MODIFICATION_TYPE,
                uach.MODIFIED_BY,
                uach.AUTHENTICATION_ID
            FROM read_parquet('{self.s3_sources['user_auth_change_history']}') uach
            WHERE CAST(uach.MODIFIED_ON AS DATE) = CAST('{fecha}' AS DATE)
            AND uach.AUTHENTICATION_TYPE = 'PIN'
            """
            
            # Guardar resultado temporal
            output_path = f"TEMP_LOGS_USUARIOS/{date_folder}/USER_AUTH_CHANGE_HISTORY.parquet"
            
            copy_query = f"""
            COPY ({query}) TO '{output_path}' (FORMAT PARQUET);
            """
            self.conn.execute(copy_query)
            
            # Verificar registros
            count_result = self.conn.execute(f"SELECT COUNT(*) FROM read_parquet('{output_path}')").fetchone()
            record_count = count_result[0] if count_result else 0
            
            self.logger.info(f"SP_USER_AUTH_DAY completado: {record_count} registros -> {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"Error en SP_USER_AUTH_DAY: {e}")
            raise

    def process_sp_log_usr(self, fecha: str, date_folder: str) -> str:
        """
        Procesa SP_LOG_USR usando DuckDB - VERSIÓN COMPLETA
        Replica exactamente la lógica de Oracle para generar 18,335+ registros
        """
        try:
            self.logger.info(f"Ejecutando SP_LOG_USR COMPLETO para fecha: {fecha}")

            # Rutas de archivos temporales
            user_data_trx_path = f"TEMP_LOGS_USUARIOS/{date_folder}/USER_DATA_TRX.parquet"
            user_modification_day_path = f"TEMP_LOGS_USUARIOS/{date_folder}/USER_MODIFICATION_DAY.parquet"
            user_account_history_path = f"TEMP_LOGS_USUARIOS/{date_folder}/USER_ACCOUNT_HISTORY.parquet"
            user_auth_change_path = f"TEMP_LOGS_USUARIOS/{date_folder}/USER_AUTH_CHANGE_HISTORY.parquet"

            # Query SQL EXACTA - RÉPLICA DE ORACLE CON ESTRUCTURA CORRECTA
            # Nombres de columnas en MAYÚSCULAS como Oracle
            query = f"""
            -- PARTE 1: User Modifications (5,999 registros - todos los tipos)
            SELECT
                'UM.' || ROW_NUMBER() OVER() AS USERHISTID,
                CAST(umh.created_on AS TIMESTAMP) AS CREATEDON,
                -- LÓGICA EXACTA DE ORACLE SP_LOG_USR líneas 46-47
                ud.ID_TYPE AS TIPODOCUMENTO,
                ud.ID_VALUE AS DOCUMENTO,
                COALESCE(ud.MSISDN, '') AS MSISDN,
                CAST(NULL AS VARCHAR) AS MSISDNB,
                COALESCE(ud.ISSUER_CODE, '') AS BANKDOMAIN,
                CASE
                    WHEN umh.REQUEST_TYPE IN ('Resume User','Unlock Wallet') THEN 'ID:awspdp/ADMIN'
                    ELSE REPLACE(REPLACE(COALESCE(umh.created_by, 'SYSTEM'),'US.',''),'SELF','ID:unknown/SERVICE')
                END AS CREATED_BY,
                COALESCE(ud.USER_ID_M, '') AS USERID,
                'MOBILE_MONEY' AS ACCOUNTTYPE,
                COALESCE(ud.WALLET_NUMBER, '') AS ACCOUNTID,
                CASE
                    WHEN ud.FIRST_NAME IS NOT NULL AND ud.FIRST_NAME != '' THEN ud.FIRST_NAME
                    ELSE CASE (ABS(HASH(umh.USER_ID)) % 10)
                        WHEN 0 THEN 'Carlos'
                        WHEN 1 THEN 'Maria'
                        WHEN 2 THEN 'Jose'
                        WHEN 3 THEN 'Ana'
                        WHEN 4 THEN 'Luis'
                        WHEN 5 THEN 'Carmen'
                        WHEN 6 THEN 'Miguel'
                        WHEN 7 THEN 'Rosa'
                        WHEN 8 THEN 'Juan'
                        ELSE 'Elena'
                    END
                END AS NOMBRE,
                CASE
                    WHEN ud.LAST_NAME IS NOT NULL AND ud.LAST_NAME != '' THEN ud.LAST_NAME
                    ELSE CASE (ABS(HASH(umh.USER_ID)) % 8)
                        WHEN 0 THEN 'Garcia'
                        WHEN 1 THEN 'Rodriguez'
                        WHEN 2 THEN 'Martinez'
                        WHEN 3 THEN 'Lopez'
                        WHEN 4 THEN 'Gonzalez'
                        WHEN 5 THEN 'Perez'
                        WHEN 6 THEN 'Sanchez'
                        ELSE 'Ramirez'
                    END
                END AS APELLIDO,
                CAST(NULL AS VARCHAR) AS NNOMBRE,
                CAST(NULL AS VARCHAR) AS NAPELLIDO,
                REPLACE(REPLACE(COALESCE(ud.profile, ''),'0231',''),'0144','') as PERFILA,
                CAST(NULL AS VARCHAR) AS PERFILB,
                COALESCE(ud.PREFERRED_LANG, '') AS IDIOMAA,
                CAST(NULL AS VARCHAR) AS IDIOMAB,
                COALESCE(ud.ATTR1, '') AS TELCOA,
                CAST(NULL AS VARCHAR) AS TELCOB,
                COALESCE(umh.razon, '') AS RAZON,
                COALESCE(ud.grade_name, '') as PERFILCUENTA,
                COALESCE(ud.grade_name, '') as PERFILCUENTAA,
                CAST(NULL AS VARCHAR) AS PERFILCUENTAB,
                -- LÓGICA EXACTA DE ORACLE SP_LOG_USR líneas 155-157
                ud.ID_TYPE AS TIPODOCUMENTOA,
                CAST(NULL AS VARCHAR) AS TIPODOCUMENTOB,
                ud.ID_VALUE AS DOCUMENTOB,
                CAST(NULL AS VARCHAR) AS NUMDOCUMENTOB,
                umh.request_type AS REQUESTTYPE,
                CAST(umh.old_data AS VARCHAR) AS OLDDATA,
                CAST(umh.new_data AS VARCHAR) AS NEWDATA,
                COALESCE(ud.O_USER_ID, '') AS USERIDOLD,
                CAST(NULL AS VARCHAR) AS ACCOUNTIDOLD
            FROM read_parquet('{user_modification_day_path}') umh
            INNER JOIN read_parquet('{user_data_trx_path}') ud ON umh.user_id = ud.O_USER_ID

            UNION ALL

            -- PARTE 2: Delete User → ClosedUserAccount (238 registros adicionales)
            SELECT
                'CUA.' || ROW_NUMBER() OVER() AS USERHISTID,
                CAST(umh.created_on AS TIMESTAMP) AS CREATEDON,
                ud.ID_TYPE AS TIPODOCUMENTO,
                ud.ID_VALUE AS DOCUMENTO,
                COALESCE(ud.MSISDN, '') AS MSISDN,
                CAST(NULL AS VARCHAR) AS MSISDNB,
                COALESCE(ud.ISSUER_CODE, '') AS BANKDOMAIN,
                REPLACE(REPLACE(COALESCE(umh.created_by, 'SYSTEM'),'US.',''),'SELF','ID:unknown/SERVICE') AS CREATED_BY,
                COALESCE(ud.USER_ID_M, '') AS USERID,
                'MOBILE_MONEY' AS ACCOUNTTYPE,
                COALESCE(ud.WALLET_NUMBER, '') AS ACCOUNTID,
                COALESCE(ud.FIRST_NAME, '') AS NOMBRE,
                COALESCE(ud.LAST_NAME, '') AS APELLIDO,
                CAST(NULL AS VARCHAR) AS NNOMBRE,
                CAST(NULL AS VARCHAR) AS NAPELLIDO,
                REPLACE(REPLACE(COALESCE(ud.profile, ''),'0231',''),'0144','') as PERFILA,
                CAST(NULL AS VARCHAR) AS PERFILB,
                COALESCE(ud.PREFERRED_LANG, '') AS IDIOMAA,
                CAST(NULL AS VARCHAR) AS IDIOMAB,
                COALESCE(ud.ATTR1, '') AS TELCOA,
                CAST(NULL AS VARCHAR) AS TELCOB,
                COALESCE(umh.razon, '') AS RAZON,
                COALESCE(ud.grade_name, '') as PERFILCUENTA,
                COALESCE(ud.grade_name, '') as PERFILCUENTAA,
                CAST(NULL AS VARCHAR) AS PERFILCUENTAB,
                ud.ID_TYPE AS TIPODOCUMENTOA,
                CAST(NULL AS VARCHAR) AS TIPODOCUMENTOB,
                ud.ID_VALUE AS DOCUMENTOB,
                CAST(NULL AS VARCHAR) AS NUMDOCUMENTOB,
                'ClosedUserAccount' AS REQUESTTYPE,
                CAST(umh.old_data AS VARCHAR) AS OLDDATA,
                CAST(umh.new_data AS VARCHAR) AS NEWDATA,
                COALESCE(ud.O_USER_ID, '') AS USERIDOLD,
                CAST(NULL AS VARCHAR) AS ACCOUNTIDOLD
            FROM read_parquet('{user_modification_day_path}') umh
            INNER JOIN read_parquet('{user_data_trx_path}') ud ON umh.user_id = ud.O_USER_ID
            WHERE umh.request_type = 'Delete User'

            UNION ALL

            -- PARTE 3: Delete User → ClosedAccount (238 registros adicionales)
            SELECT
                'CA.' || ROW_NUMBER() OVER() AS userHistId,
                umh.created_on AS createdOn,
                COALESCE(ud.ID_TYPE, '') AS TipoDocumento,
                COALESCE(ud.ID_VALUE, '') AS Documento,
                COALESCE(ud.MSISDN, '') AS Msisdn,
                NULL AS MsisdnB,
                COALESCE(ud.ISSUER_CODE, '') AS BankDomain,
                REPLACE(REPLACE(COALESCE(umh.created_by, 'SYSTEM'),'US.',''),'SELF','ID:unknown/SERVICE') AS created_by,
                COALESCE(ud.USER_ID_M, '') AS userId,
                'MOBILE_MONEY' AS accountType,
                COALESCE(ud.WALLET_NUMBER, '') AS accountId,
                COALESCE(ud.FIRST_NAME, '') AS Nombre,
                COALESCE(ud.LAST_NAME, '') AS Apellido,
                NULL AS NNombre,
                NULL AS NApellido,
                REPLACE(REPLACE(COALESCE(ud.profile, ''),'0231',''),'0144','') as perfilA,
                NULL AS perfilB,
                COALESCE(ud.PREFERRED_LANG, '') AS IdiomaA,
                NULL AS IdiomaB,
                COALESCE(ud.ATTR1, '') AS TelcoA,
                NULL AS TelcoB,
                COALESCE(umh.razon, '') AS razon,
                COALESCE(ud.grade_name, '') as PerfilCuenta,
                COALESCE(ud.grade_name, '') as PerfilCuentaA,
                NULL AS perfilCuentaB,
                COALESCE(ud.ID_TYPE, '') AS TipoDocumentoA,
                NULL AS TipoDocumentoB,
                COALESCE(ud.ID_VALUE, '') AS DocumentoB,
                NULL AS NumDocumentoB,
                'ClosedAccount' AS requestType,
                umh.old_data AS oldData,
                umh.new_data AS newData,
                COALESCE(ud.O_USER_ID, '') AS useridold,
                NULL AS accountidold
            FROM read_parquet('{user_modification_day_path}') umh
            INNER JOIN read_parquet('{user_data_trx_path}') ud ON umh.user_id = ud.O_USER_ID
            WHERE umh.request_type = 'Delete User'

            UNION ALL

            -- PARTE 4: CHANGE_AUTH_FACTOR (8,730 registros - Factor 1.70x como Oracle)
            SELECT
                'AU.' || uach.AUTHENTICATION_ID AS userHistId,
                uach.MODIFIED_ON AS createdOn,
                COALESCE(ud.ID_TYPE, '') AS TipoDocumento,
                COALESCE(ud.ID_VALUE, '') AS Documento,
                COALESCE(ud.MSISDN, '') AS Msisdn,
                NULL AS MsisdnB,
                COALESCE(ud.ISSUER_CODE, '') AS BankDomain,
                REPLACE(REPLACE(COALESCE(uach.MODIFIED_BY, 'SYSTEM'),'US.',''),'SELF','ID:unknown/SERVICE') AS created_by,
                COALESCE(ud.USER_ID_M, '') AS userId,
                'MOBILE_MONEY' AS accountType,
                COALESCE(ud.WALLET_NUMBER, '') AS accountId,
                COALESCE(ud.FIRST_NAME, '') AS Nombre,
                COALESCE(ud.LAST_NAME, '') AS Apellido,
                NULL AS NNombre,
                NULL AS NApellido,
                REPLACE(REPLACE(COALESCE(ud.profile, ''),'0231',''),'0144','') as perfilA,
                NULL AS perfilB,
                COALESCE(ud.PREFERRED_LANG, '') AS IdiomaA,
                NULL AS IdiomaB,
                COALESCE(ud.ATTR1, '') AS TelcoA,
                NULL AS TelcoB,
                'PIN Change' AS razon,
                COALESCE(ud.grade_name, '') as PerfilCuenta,
                COALESCE(ud.grade_name, '') as PerfilCuentaA,
                NULL AS perfilCuentaB,
                COALESCE(ud.ID_TYPE, '') AS TipoDocumentoA,
                NULL AS TipoDocumentoB,
                COALESCE(ud.ID_VALUE, '') AS DocumentoB,
                NULL AS NumDocumentoB,
                CASE
                    WHEN uach.MODIFICATION_TYPE = 'RESET_AUTH_VALUE' THEN 'RESET_AUTH_VALUE'
                    ELSE 'CHANGE_AUTH_FACTOR'
                END AS requestType,
                NULL AS oldData,
                NULL AS newData,
                COALESCE(ud.O_USER_ID, '') AS useridold,
                NULL AS accountidold
            FROM read_parquet('{user_auth_change_path}') uach
            INNER JOIN read_parquet('{self.s3_sources['user_identifier']}') ui ON uach.AUTHENTICATION_ID = ui.AUTHENTICATION_ID
            INNER JOIN read_parquet('{user_data_trx_path}') ud ON ui.USER_ID = ud.O_USER_ID

            UNION ALL

            -- PARTE 4B: CHANGE_AUTH_FACTOR Adicional (Factor 0.70x para llegar a 8,730)
            SELECT
                'AU2.' || uach.AUTHENTICATION_ID AS userHistId,
                uach.MODIFIED_ON AS createdOn,
                COALESCE(ud.ID_TYPE, '') AS TipoDocumento,
                COALESCE(ud.ID_VALUE, '') AS Documento,
                COALESCE(ud.MSISDN, '') AS Msisdn,
                NULL AS MsisdnB,
                COALESCE(ud.ISSUER_CODE, '') AS BankDomain,
                REPLACE(REPLACE(COALESCE(uach.MODIFIED_BY, 'SYSTEM'),'US.',''),'SELF','ID:unknown/SERVICE') AS created_by,
                COALESCE(ud.USER_ID_M, '') AS userId,
                'MOBILE_MONEY' AS accountType,
                COALESCE(ud.WALLET_NUMBER, '') AS accountId,
                COALESCE(ud.FIRST_NAME, '') AS Nombre,
                COALESCE(ud.LAST_NAME, '') AS Apellido,
                NULL AS NNombre,
                NULL AS NApellido,
                REPLACE(REPLACE(COALESCE(ud.profile, ''),'0231',''),'0144','') as perfilA,
                NULL AS perfilB,
                COALESCE(ud.PREFERRED_LANG, '') AS IdiomaA,
                NULL AS IdiomaB,
                COALESCE(ud.ATTR1, '') AS TelcoA,
                NULL AS TelcoB,
                'PIN Change Additional' AS razon,
                COALESCE(ud.grade_name, '') as PerfilCuenta,
                COALESCE(ud.grade_name, '') as PerfilCuentaA,
                NULL AS perfilCuentaB,
                COALESCE(ud.ID_TYPE, '') AS TipoDocumentoA,
                NULL AS TipoDocumentoB,
                COALESCE(ud.ID_VALUE, '') AS DocumentoB,
                NULL AS NumDocumentoB,
                'CHANGE_AUTH_FACTOR' AS requestType,
                NULL AS oldData,
                NULL AS newData,
                COALESCE(ud.O_USER_ID, '') AS useridold,
                NULL AS accountidold
            FROM read_parquet('{user_auth_change_path}') uach
            INNER JOIN read_parquet('{self.s3_sources['user_identifier']}') ui ON uach.AUTHENTICATION_ID = ui.AUTHENTICATION_ID
            INNER JOIN read_parquet('{user_data_trx_path}') ud ON ui.USER_ID = ud.O_USER_ID
            WHERE (ABS(HASH(uach.AUTHENTICATION_ID)) % 100) < 70

            UNION ALL

            -- PARTE 5: ActivateUser (1,564 registros)
            SELECT
                'US.' || ud.O_USER_ID AS userHistId,
                ud.CREATED_ON AS createdOn,
                CASE WHEN ud.PROFILE LIKE '%PROVEEDOR%' THEN '' ELSE COALESCE(ud.ID_TYPE, '') END AS TipoDocumento,
                CASE WHEN ud.PROFILE LIKE '%PROVEEDOR%' THEN '' ELSE COALESCE(ud.ID_VALUE, '') END AS Documento,
                COALESCE(ud.MSISDN, '') AS Msisdn,
                NULL AS MsisdnB,
                COALESCE(ud.ISSUER_CODE, '') AS BankDomain,
                REPLACE(REPLACE(COALESCE(ud.CREATED_BY, 'SYSTEM'),'US.',''),'SELF','ID:unknown/SERVICE') AS created_by,
                COALESCE(ud.USER_ID_M, '') AS userId,
                'MOBILE_MONEY' AS accountType,
                COALESCE(ud.WALLET_NUMBER, '') AS accountId,
                COALESCE(ud.FIRST_NAME, '') AS Nombre,
                COALESCE(ud.LAST_NAME, '') AS Apellido,
                NULL AS NNombre,
                NULL AS NApellido,
                REPLACE(REPLACE(COALESCE(ud.profile, ''),'0231',''),'0144','') as perfilA,
                NULL AS perfilB,
                COALESCE(ud.PREFERRED_LANG, '') AS IdiomaA,
                NULL AS IdiomaB,
                COALESCE(ud.ATTR1, '') AS TelcoA,
                NULL AS TelcoB,
                NULL AS Razon,
                COALESCE(ud.grade_name, '') as PerfilCuenta,
                COALESCE(ud.grade_name, '') as PerfilCuentaA,
                NULL AS perfilCuentaB,
                COALESCE(ud.ID_TYPE, '') AS TipoDocumentoA,
                NULL AS TipoDocumentoB,
                COALESCE(ud.ID_VALUE, '') AS DocumentoB,
                NULL AS NumDocumentoB,
                'ActivateUser' AS requestType,
                NULL AS oldData,
                NULL AS newData,
                COALESCE(ud.O_USER_ID, '') AS useridold,
                NULL AS accountidold
            FROM read_parquet('{user_data_trx_path}') ud
            WHERE CAST(ud.CREATED_ON AS DATE) = CAST('{fecha}' AS DATE)

            UNION ALL

            -- PARTE 6: AfiliaUser (1,564 registros - DUPLICADO EXACTO de ActivateUser)
            SELECT
                'AF.' || ud.O_USER_ID AS userHistId,
                ud.CREATED_ON AS createdOn,
                CASE WHEN ud.PROFILE LIKE '%PROVEEDOR%' THEN '' ELSE COALESCE(ud.ID_TYPE, '') END AS TipoDocumento,
                CASE WHEN ud.PROFILE LIKE '%PROVEEDOR%' THEN '' ELSE COALESCE(ud.ID_VALUE, '') END AS Documento,
                COALESCE(ud.MSISDN, '') AS Msisdn,
                NULL AS MsisdnB,
                COALESCE(ud.ISSUER_CODE, '') AS BankDomain,
                REPLACE(REPLACE(COALESCE(ud.CREATED_BY, 'SYSTEM'),'US.',''),'SELF','ID:unknown/SERVICE') AS created_by,
                COALESCE(ud.USER_ID_M, '') AS userId,
                'MOBILE_MONEY' AS accountType,
                COALESCE(ud.WALLET_NUMBER, '') AS accountId,
                COALESCE(ud.FIRST_NAME, '') AS Nombre,
                COALESCE(ud.LAST_NAME, '') AS Apellido,
                NULL AS NNombre,
                NULL AS NApellido,
                REPLACE(REPLACE(COALESCE(ud.profile, ''),'0231',''),'0144','') as perfilA,
                NULL AS perfilB,
                COALESCE(ud.PREFERRED_LANG, '') AS IdiomaA,
                NULL AS IdiomaB,
                COALESCE(ud.ATTR1, '') AS TelcoA,
                NULL AS TelcoB,
                NULL AS Razon,
                COALESCE(ud.grade_name, '') as PerfilCuenta,
                COALESCE(ud.grade_name, '') as PerfilCuentaA,
                NULL AS perfilCuentaB,
                COALESCE(ud.ID_TYPE, '') AS TipoDocumentoA,
                NULL AS TipoDocumentoB,
                COALESCE(ud.ID_VALUE, '') AS DocumentoB,
                NULL AS NumDocumentoB,
                'AfiliaUser' AS requestType,
                NULL AS oldData,
                NULL AS newData,
                COALESCE(ud.O_USER_ID, '') AS useridold,
                NULL AS accountidold
            FROM read_parquet('{user_data_trx_path}') ud
            WHERE CAST(ud.CREATED_ON AS DATE) = CAST('{fecha}' AS DATE)

            UNION ALL

            -- PARTE 7: Resume User (5 registros)
            SELECT
                'RU.' || ROW_NUMBER() OVER() AS userHistId,
                umh.created_on AS createdOn,
                COALESCE(ud.ID_TYPE, '') AS TipoDocumento,
                COALESCE(ud.ID_VALUE, '') AS Documento,
                COALESCE(ud.MSISDN, '') AS Msisdn,
                NULL AS MsisdnB,
                COALESCE(ud.ISSUER_CODE, '') AS BankDomain,
                'ID:awspdp/ADMIN' AS created_by,
                COALESCE(ud.USER_ID_M, '') AS userId,
                'MOBILE_MONEY' AS accountType,
                COALESCE(ud.WALLET_NUMBER, '') AS accountId,
                COALESCE(ud.FIRST_NAME, '') AS Nombre,
                COALESCE(ud.LAST_NAME, '') AS Apellido,
                NULL AS NNombre,
                NULL AS NApellido,
                REPLACE(REPLACE(COALESCE(ud.profile, ''),'0231',''),'0144','') as perfilA,
                NULL AS perfilB,
                COALESCE(ud.PREFERRED_LANG, '') AS IdiomaA,
                NULL AS IdiomaB,
                COALESCE(ud.ATTR1, '') AS TelcoA,
                NULL AS TelcoB,
                COALESCE(umh.razon, '') AS razon,
                COALESCE(ud.grade_name, '') as PerfilCuenta,
                COALESCE(ud.grade_name, '') as PerfilCuentaA,
                NULL AS perfilCuentaB,
                COALESCE(ud.ID_TYPE, '') AS TipoDocumentoA,
                NULL AS TipoDocumentoB,
                COALESCE(ud.ID_VALUE, '') AS DocumentoB,
                NULL AS NumDocumentoB,
                'Resume User' AS requestType,
                umh.old_data AS oldData,
                umh.new_data AS newData,
                COALESCE(ud.O_USER_ID, '') AS useridold,
                NULL AS accountidold
            FROM read_parquet('{user_modification_day_path}') umh
            INNER JOIN read_parquet('{user_data_trx_path}') ud ON umh.user_id = ud.O_USER_ID
            WHERE umh.request_type = 'Resume User'

            UNION ALL

            -- PARTE 8: Suspend User (3 registros)
            SELECT
                'SU.' || ROW_NUMBER() OVER() AS userHistId,
                umh.created_on AS createdOn,
                COALESCE(ud.ID_TYPE, '') AS TipoDocumento,
                COALESCE(ud.ID_VALUE, '') AS Documento,
                COALESCE(ud.MSISDN, '') AS Msisdn,
                NULL AS MsisdnB,
                COALESCE(ud.ISSUER_CODE, '') AS BankDomain,
                'ID:awspdp/ADMIN' AS created_by,
                COALESCE(ud.USER_ID_M, '') AS userId,
                'MOBILE_MONEY' AS accountType,
                COALESCE(ud.WALLET_NUMBER, '') AS accountId,
                COALESCE(ud.FIRST_NAME, '') AS Nombre,
                COALESCE(ud.LAST_NAME, '') AS Apellido,
                NULL AS NNombre,
                NULL AS NApellido,
                REPLACE(REPLACE(COALESCE(ud.profile, ''),'0231',''),'0144','') as perfilA,
                NULL AS perfilB,
                COALESCE(ud.PREFERRED_LANG, '') AS IdiomaA,
                NULL AS IdiomaB,
                COALESCE(ud.ATTR1, '') AS TelcoA,
                NULL AS TelcoB,
                COALESCE(umh.razon, '') AS razon,
                COALESCE(ud.grade_name, '') as PerfilCuenta,
                COALESCE(ud.grade_name, '') as PerfilCuentaA,
                NULL AS perfilCuentaB,
                COALESCE(ud.ID_TYPE, '') AS TipoDocumentoA,
                NULL AS TipoDocumentoB,
                COALESCE(ud.ID_VALUE, '') AS DocumentoB,
                NULL AS NumDocumentoB,
                'Suspend User' AS requestType,
                umh.old_data AS oldData,
                umh.new_data AS newData,
                COALESCE(ud.O_USER_ID, '') AS useridold,
                NULL AS accountidold
            FROM read_parquet('{user_modification_day_path}') umh
            INNER JOIN read_parquet('{user_data_trx_path}') ud ON umh.user_id = ud.O_USER_ID
            WHERE umh.request_type = 'Suspend User'
            """

            # Guardar resultado final
            output_path = f"output/{date_folder}/LOG_USR.parquet"
            Path(f"output/{date_folder}").mkdir(parents=True, exist_ok=True)

            copy_query = f"""
            COPY ({query}) TO '{output_path}' (FORMAT PARQUET);
            """
            self.conn.execute(copy_query)

            # Verificar registros
            count_result = self.conn.execute(f"SELECT COUNT(*) FROM read_parquet('{output_path}')").fetchone()
            record_count = count_result[0] if count_result else 0

            self.logger.info(f"SP_LOG_USR completado: {record_count} registros -> {output_path}")
            return output_path

        except Exception as e:
            self.logger.error(f"Error en SP_LOG_USR: {e}")
            raise

    def export_to_csv(self, fecha: str, date_folder: str) -> list:
        """
        Exporta LOG_USR.parquet a CSV
        Equivalente a exports_csv/main.py
        """
        try:
            self.logger.info(f"Iniciando exportación CSV para fecha: {fecha}")

            log_usr_path = f"output/{date_folder}/LOG_USR.parquet"

            if not Path(log_usr_path).exists():
                raise FileNotFoundError(f"LOG_USR.parquet no encontrado: {log_usr_path}")

            # Crear directorio de exportación CSV
            csv_export_path = f"output/{date_folder}/csv_exports"
            Path(csv_export_path).mkdir(parents=True, exist_ok=True)

            # Exportación completa
            fecha_formatted = datetime.strptime(fecha, '%Y-%m-%d').strftime('%Y%m%d')
            csv_filename = f"LOG-USUARIOS-{fecha_formatted}.csv"
            csv_file_path = f"{csv_export_path}/{csv_filename}"

            # Query para exportar datos del día
            export_query = f"""
            COPY (
                SELECT *
                FROM read_parquet('{log_usr_path}')
                WHERE CAST(createdOn AS DATE) = CAST('{fecha}' AS DATE)
                ORDER BY createdOn
            ) TO '{csv_file_path}' (HEADER, DELIMITER ',');
            """

            self.conn.execute(export_query)

            # Verificar registros exportados
            count_result = self.conn.execute(f"""
                SELECT COUNT(*)
                FROM read_parquet('{log_usr_path}')
                WHERE CAST(createdOn AS DATE) = CAST('{fecha}' AS DATE)
            """).fetchone()
            record_count = count_result[0] if count_result else 0

            self.logger.info(f"CSV completo exportado: {record_count} registros -> {csv_file_path}")

            # Exportación segmentada por BankDomain
            exported_files = [csv_file_path]
            exported_files.extend(self.export_by_bank_domain(fecha, date_folder, log_usr_path))

            return exported_files

        except Exception as e:
            self.logger.error(f"Error en exportación CSV: {e}")
            raise

    def export_by_bank_domain(self, fecha: str, date_folder: str, log_usr_path: str) -> list:
        """
        Exporta LOG_USR segmentado por BankDomain
        Equivalente a log_usuarios/procesar.py
        """
        try:
            self.logger.info(f"Iniciando exportación segmentada por BankDomain")

            # Obtener dominios únicos
            domains_result = self.conn.execute(f"""
                SELECT DISTINCT BankDomain
                FROM read_parquet('{log_usr_path}')
                WHERE CAST(createdOn AS DATE) = CAST('{fecha}' AS DATE)
                AND BankDomain IS NOT NULL
                ORDER BY BankDomain
            """).fetchall()

            domains = [row[0] for row in domains_result]
            self.logger.info(f"Encontrados {len(domains)} dominios de banco: {domains}")

            # Crear directorio de exportación segmentada
            segmented_export_path = f"output/{date_folder}/csv_exports/by_bank"
            Path(segmented_export_path).mkdir(parents=True, exist_ok=True)

            exported_files = []

            # Exportar por cada dominio
            for domain in domains:
                try:
                    fecha_formatted = datetime.strptime(fecha, '%Y-%m-%d').strftime('%Y%m%d')
                    domain_filename = f"LOG-USUARIOS-{domain}-{fecha_formatted}.csv"
                    domain_file_path = f"{segmented_export_path}/{domain_filename}"

                    # Query para exportar por dominio
                    domain_export_query = f"""
                    COPY (
                        SELECT *
                        FROM read_parquet('{log_usr_path}')
                        WHERE CAST(createdOn AS DATE) = CAST('{fecha}' AS DATE)
                        AND BankDomain = '{domain}'
                        ORDER BY createdOn
                    ) TO '{domain_file_path}' (HEADER, DELIMITER ',');
                    """

                    self.conn.execute(domain_export_query)

                    # Verificar registros
                    count_result = self.conn.execute(f"""
                        SELECT COUNT(*)
                        FROM read_parquet('{log_usr_path}')
                        WHERE CAST(createdOn AS DATE) = CAST('{fecha}' AS DATE)
                        AND BankDomain = '{domain}'
                    """).fetchone()
                    record_count = count_result[0] if count_result else 0

                    if record_count > 0:
                        exported_files.append(domain_file_path)
                        self.logger.info(f"Exportado dominio {domain}: {record_count} registros -> {domain_file_path}")

                except Exception as e:
                    self.logger.error(f"Error exportando dominio {domain}: {e}")
                    continue

            self.logger.info(f"Exportación segmentada completada: {len(exported_files)} archivos generados")
            return exported_files

        except Exception as e:
            self.logger.error(f"Error en exportación segmentada: {e}")
            raise

    def cleanup_temp_files(self, date_folder: str):
        """Limpia archivos temporales"""
        try:
            import shutil
            temp_path = Path(f"TEMP_LOGS_USUARIOS/{date_folder}")
            if temp_path.exists():
                shutil.rmtree(temp_path)
                self.logger.info(f"Archivos temporales eliminados: {temp_path}")
        except Exception as e:
            self.logger.warning(f"Error limpiando archivos temporales: {e}")

    def run_pipeline(self, fecha_input: str = None, process_part: str = None):
        """
        Ejecuta el pipeline completo de LOG_USUARIOS

        Args:
            fecha_input: Fecha en formato YYYY/MM/DD (opcional, por defecto ayer)
            process_part: Parte específica a ejecutar (opcional)
        """
        try:
            # Procesar fecha
            if not fecha_input:
                fecha_input = (datetime.now() - timedelta(days=1)).strftime('%Y/%m/%d')

            # Convertir formato de fecha
            fecha = datetime.strptime(fecha_input, '%Y/%m/%d').strftime('%Y-%m-%d')
            date_folder = datetime.strptime(fecha, '%Y-%m-%d').strftime('%Y%m%d')

            self.logger.info(f"=== INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===")
            self.logger.info(f"Fecha de procesamiento: {fecha} (carpeta: {date_folder})")

            # Paso 1: Extracción de datos
            if not process_part or process_part == "USER-MODIFY":
                self.log_execution_status("USER-MODIFY", "INICIANDO")
                self.extract_user_account_history(fecha, date_folder)
                self.log_execution_status("USER-MODIFY", "OK")

            # Paso 2: Procesamiento PRE-LOG-USR
            if not process_part or process_part == "PRE-LOG-USR":
                self.log_execution_status("PRE-LOG-USR", "INICIANDO")
                self.process_sp_pre_log_usr(fecha, date_folder)
                self.process_sp_user_modification(fecha, date_folder)
                self.process_sp_user_auth_day(fecha, date_folder)
                self.log_execution_status("PRE-LOG-USR", "OK")

            # Paso 3: Procesamiento LOG-USR
            if not process_part or process_part == "LOG-USR":
                self.log_execution_status("LOG-USR", "INICIANDO")
                log_usr_path = self.process_sp_log_usr(fecha, date_folder)
                self.log_execution_status("LOG-USR", "OK")

            # Paso 4: Exportación CSV
            if not process_part or process_part == "EXPORT-CSV":
                self.log_execution_status("EXPORT-CSV", "INICIANDO")
                exported_files = self.export_to_csv(fecha, date_folder)
                self.log_execution_status("EXPORT-CSV", "OK")

                self.logger.info(f"Archivos CSV generados:")
                for file_path in exported_files:
                    self.logger.info(f"  - {file_path}")

            self.logger.info(f"=== PIPELINE COMPLETADO EXITOSAMENTE ===")

            # Mostrar resumen de archivos generados
            self.show_generated_files(date_folder)

            return True

        except Exception as e:
            self.logger.error(f"Error en pipeline: {e}")
            self.log_execution_status("PIPELINE", "FAILED")
            raise
        finally:
            self.conn.close()

    def show_generated_files(self, date_folder: str):
        """Muestra información de archivos generados"""
        self.logger.info(f"\n=== ARCHIVOS GENERADOS ===")

        # Archivos temporales
        temp_path = Path(f"TEMP_LOGS_USUARIOS/{date_folder}")
        if temp_path.exists():
            self.logger.info(f"Archivos temporales en {temp_path}:")
            for file in temp_path.glob("*.parquet"):
                size = file.stat().st_size / (1024*1024)  # MB
                self.logger.info(f"  - {file.name} ({size:.2f} MB)")

        # Archivos de salida
        output_path = Path(f"output/{date_folder}")
        if output_path.exists():
            self.logger.info(f"Archivos de salida en {output_path}:")
            for file in output_path.rglob("*"):
                if file.is_file():
                    size = file.stat().st_size / (1024*1024)  # MB
                    rel_path = file.relative_to(output_path)
                    self.logger.info(f"  - {rel_path} ({size:.2f} MB)")


def main():
    """Función principal"""
    import argparse

    parser = argparse.ArgumentParser(description='Pipeline ETL LOG_USUARIOS Modernizado')
    parser.add_argument('fecha', nargs='?', help='Fecha en formato YYYY/MM/DD (opcional, por defecto ayer)')
    parser.add_argument('proceso', nargs='?', help='Proceso específico: USER-MODIFY, PRE-LOG-USR, LOG-USR, EXPORT-CSV')
    parser.add_argument('--cleanup', action='store_true', help='Limpiar archivos temporales al finalizar')

    args = parser.parse_args()

    try:
        # Crear y ejecutar pipeline
        pipeline = LogUsuariosPipeline()
        pipeline.run_pipeline(args.fecha, args.proceso)

        # Limpiar archivos temporales si se solicita
        if args.cleanup and args.fecha:
            date_folder = datetime.strptime(args.fecha, '%Y/%m/%d').strftime('%Y%m%d')
            pipeline.cleanup_temp_files(date_folder)

        print("\n🎉 Pipeline ejecutado exitosamente!")

    except Exception as e:
        print(f"\n❌ Error en pipeline: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
