#!/usr/bin/env python3
"""
Análisis detallado del campo TIPODOCUMENTO entre Oracle original y Pipeline DuckDB
"""

print("=== ANÁLISIS CAMPO TIPODOCUMENTO: ORACLE vs DUCKDB ===\n")

# Análisis del procedimiento Oracle original SP_LOG_USR.sql
print("📋 ORACLE ORIGINAL - Lógica TIPODOCUMENTO por UNION:")
print("------------------------------------------------------")

oracle_unions = {
    "UNION 1": {
        "descripcion": "USER_MODIFICATION_DAY (User Modifications)",
        "logica": "ud.ID_TYPE AS TipoDocumento",
        "linea": "línea 38 en SP_LOG_USR.sql"
    },
    "UNION 2": {
        "descripcion": "USER_AUTH_CHANGE_HISTORY (Auth Changes)", 
        "logica": "ud.ID_TYPE AS TipoDocumento",
        "linea": "línea 79 en SP_LOG_USR.sql"
    },
    "UNION 3": {
        "descripcion": "USER_DATA_TRX con ActivateUser/AfiliaUser",
        "logica": "CASE WHEN UD.PROFILE LIKE '%PROVEEDOR%' THEN '' ELSE ud.ID_TYPE END AS TipoDocumento",
        "linea": "línea 118 en SP_LOG_USR.sql"
    },
    "UNION 4": {
        "descripcion": "USER_DATA_TRX con ClosedAccount/ClosedUserAccount",
        "logica": "ud.ID_TYPE AS TipoDocumento", 
        "linea": "línea 169 en SP_LOG_USR.sql"
    }
}

for union_id, info in oracle_unions.items():
    print(f"{union_id}: {info['descripcion']}")
    print(f"   Lógica: {info['logica']}")
    print(f"   Ubicación: {info['linea']}")
    print()

print("\n📋 PIPELINE DUCKDB - Implementación actual:")
print("-------------------------------------------")

# Leer el pipeline actual para analizar
with open('/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/pipeline_log_usuarios_duckdb.py', 'r') as f:
    pipeline_content = f.read()

# Buscar todas las implementaciones de TIPODOCUMENTO
import re

# Buscar líneas que contienen TIPODOCUMENTO
tipodocumento_lines = []
lines = pipeline_content.split('\n')
for i, line in enumerate(lines, 1):
    if 'AS TIPODOCUMENTO' in line or 'AS TipoDocumento' in line:
        # Buscar el contexto de la parte
        parte_context = ""
        for j in range(max(0, i-10), i):
            if 'PARTE' in lines[j]:
                parte_context = lines[j].strip()
                break
        
        tipodocumento_lines.append({
            'linea': i,
            'contenido': line.strip(),
            'parte': parte_context
        })

for item in tipodocumento_lines:
    print(f"Línea {item['linea']}: {item['parte']}")
    print(f"   Implementación: {item['contenido']}")
    print()

print("\n🔍 VALIDACIÓN DETALLADA:")
print("========================")

# Verificar si hay discrepancias
case_when_count = sum(1 for item in tipodocumento_lines if 'CASE WHEN' in item['contenido'])
simple_id_type_count = sum(1 for item in tipodocumento_lines if 'COALESCE(ud.ID_TYPE' in item['contenido'])

print(f"✅ Implementaciones con CASE WHEN PROVEEDOR: {case_when_count}")
print(f"✅ Implementaciones con ID_TYPE simple: {simple_id_type_count}")
print(f"✅ Total de implementaciones TIPODOCUMENTO: {len(tipodocumento_lines)}")

print("\n🎯 COMPARACIÓN ORACLE vs DUCKDB:")
print("=================================")
print("Oracle UNION 1 (User Modifications): ud.ID_TYPE")
print("Oracle UNION 2 (Auth Changes): ud.ID_TYPE") 
print("Oracle UNION 3 (ActivateUser/AfiliaUser): CASE WHEN PROFILE LIKE '%PROVEEDOR%'")
print("Oracle UNION 4 (ClosedAccount/ClosedUserAccount): ud.ID_TYPE")
print()
print("DuckDB debe tener:")
print("- Partes 1,2,3,4,7,8: COALESCE(ud.ID_TYPE, '') ✓")
print("- Partes 5,6: CASE WHEN ud.PROFILE LIKE '%PROVEEDOR%' ✓")
