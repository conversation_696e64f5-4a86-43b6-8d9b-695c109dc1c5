# Pipeline ETL LOG_USUARIOS Modernizado

## Descripción

Este proyecto es una versión modernizada del proceso ETL `ejecuta_log_user.sh` original, migrado de Oracle a un pipeline basado en archivos Parquet almacenados en S3. Mantiene exactamente la misma lógica de negocio pero utiliza tecnologías modernas para mejorar el rendimiento y la escalabilidad.

## Arquitectura

### Antes (Original)
- **Fuentes**: Oracle (PDP_PROD10_MAINDB, PDP_PROD10_MAINDBBUS) + MySQL
- **Procesamiento**: Stored Procedures en Oracle
- **Temporales**: Tablas Oracle (USR_DATALAKE)
- **Salida**: Tabla Oracle + CSV

### Después (Modernizado)
- **Fuentes**: Archivos Parquet en S3
- **Procesamiento**: DuckDB + Python
- **Temporales**: Archivos Parquet locales
- **Salida**: Archivos Parquet + CSV

## Estructura del Proyecto

```
S3_LOG_USER/
├── config/
│   ├── s3_sources.yaml          # Configuración de fuentes S3
│   └── processing_config.yaml   # Configuración del pipeline
├── src/
│   ├── extractors/
│   │   └── mysql_extractor.py   # Extracción desde MySQL/S3
│   ├── processors/
│   │   ├── pre_log_usr.py       # Equivalente a SP_PRE_LOG_USR
│   │   ├── user_modification.py # Equivalente a SP_USER_MODIFICATION
│   │   ├── user_auth_day.py     # Equivalente a SP_USER_AUTH_DAY
│   │   └── log_usr.py           # Equivalente a SP_LOG_USR
│   ├── exporters/
│   │   └── csv_exporter.py      # Exportación a CSV
│   ├── utils/
│   │   ├── parquet_handler.py   # Utilidades para Parquet
│   │   └── logger.py            # Sistema de logging
│   └── main.py                  # Orquestador principal
├── TEMP_LOGS_USUARIOS/          # Archivos temporales
├── output/                      # Archivos finales
├── logs/                        # Logs de ejecución
├── ejecuta_log_user_parquet.sh  # Script principal modernizado
├── requirements.txt             # Dependencias Python
├── .env.example                 # Variables de entorno
└── README.md                    # Este archivo
```

## Instalación

1. **Clonar/Acceder al directorio del proyecto**
   ```bash
   cd S3_LOG_USER
   ```

2. **Crear entorno virtual**
   ```bash
   python3 -m venv venv
   source venv/bin/activate
   ```

3. **Instalar dependencias**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configurar variables de entorno**
   ```bash
   cp .env.example .env
   # Editar .env con tus credenciales
   ```

## Uso

### Ejecución Completa
```bash
# Ejecutar para ayer (por defecto)
./ejecuta_log_user_parquet.sh

# Ejecutar para fecha específica
./ejecuta_log_user_parquet.sh "2025/01/15"
```

### Ejecución de Partes Específicas
```bash
# Solo PRE-LOG-USR
python3 src/main.py "2025/01/15" "PRE-LOG-USR"

# Solo LOG-USR
python3 src/main.py "2025/01/15" "LOG-USR"
```

### Exportación CSV
```bash
# Exportar a CSV
python3 -c "
from src.exporters.csv_exporter import CSVExporter
exporter = CSVExporter()
exporter.export_log_usuarios('2025-01-15')
exporter.export_by_bank_domain('2025-01-15')
"
```

## Mapeo de Componentes

| Componente Original | Componente Modernizado | Descripción |
|-------------------|----------------------|-------------|
| `ejecuta_log_user.sh` | `ejecuta_log_user_parquet.sh` | Script principal |
| `prepare/main.py` | `src/main.py` | Orquestador |
| `mysql_extract.py` | `src/extractors/mysql_extractor.py` | Extracción MySQL |
| `SP_PRE_LOG_USR` | `src/processors/pre_log_usr.py` | Preparación datos |
| `SP_USER_MODIFICATION` | `src/processors/user_modification.py` | Modificaciones |
| `SP_USER_AUTH_DAY` | `src/processors/user_auth_day.py` | Autenticaciones |
| `SP_LOG_USR` | `src/processors/log_usr.py` | Log final |
| `exports_csv/main.py` | `src/exporters/csv_exporter.py` | Exportación CSV |

## Archivos Generados

### Temporales (TEMP_LOGS_USUARIOS/{YYYYMMDD}/)
- `USER_DATA_TRX.parquet` - Datos consolidados de usuarios
- `USER_MODIFICATION_DAY.parquet` - Modificaciones del día
- `USER_AUTH_CHANGE_HISTORY.parquet` - Cambios de autenticación
- `USER_ACCOUNT_HISTORY.parquet` - Historial de cuentas

### Finales (output/{YYYYMMDD}/)
- `LOG_USR.parquet` - Resultado final del ETL
- `csv_exports/LOG-USUARIOS-{YYYYMMDD}.csv` - Exportación CSV completa
- `csv_exports/by_bank/LOG-USUARIOS-{BANK}-{YYYYMMDD}.csv` - Por banco

## Ventajas del Nuevo Pipeline

1. **Rendimiento**: DuckDB es extremadamente rápido para consultas analíticas
2. **Escalabilidad**: Archivos Parquet son más eficientes que tablas Oracle
3. **Costo**: No requiere licencias Oracle para procesamiento
4. **Mantenibilidad**: Código Python más fácil de mantener que PL/SQL
5. **Portabilidad**: Puede ejecutarse en cualquier entorno con Python
6. **Observabilidad**: Logging detallado y monitoreo mejorado

## Configuración S3

Las fuentes S3 están configuradas en `config/s3_sources.yaml`. Asegúrate de que las credenciales AWS estén configuradas correctamente.

## Troubleshooting

### Error de conexión S3
```bash
# Verificar credenciales AWS
aws s3 ls s3://prd-datalake-silver-zone-************/ --profile your-profile
```

### Error de memoria
- Ajustar `memory_limit_gb` en `config/processing_config.yaml`
- Usar `chunk_size` más pequeño para datasets grandes

### Error de timeout
- Ajustar `MAX_TIME` en `ejecuta_log_user_parquet.sh`
- Verificar rendimiento de red S3

## Monitoreo

Los logs se generan en:
- `logs/{proceso}_{YYYYMMDD}.log` - Logs detallados por proceso
- `execution_status.log` - Estado de ejecución general

## Contacto

Para soporte técnico o preguntas sobre la migración, contactar al equipo de Data Engineering.
