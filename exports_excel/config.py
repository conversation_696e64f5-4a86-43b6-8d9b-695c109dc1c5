import os
#output_route = os.environ.get("OUTPUT_ROUTE")
output_route = "/home/<USER>/output/excel/"
emisores = ["FCOMPARTAMOS", "BNACION", "CRANDES", "CCUSCO", "FCONFIANZA", "FQAPAQ"]
private_key_path = "/home/<USER>/generate/FileSigner/pdp_sign.key"
#public_key_path = os.environ.get("PUBLIC_KEY_PATH")
public_key_path = "/home/<USER>/generate/FileSigner/SignFileNC.crt"
bucket_s3 = "prd-datalake-reports-637423440311"
#Reportes que deben sair firmados
apps_signed_obligatory = ["TRAZA-FEE","DEPOSITOS","RETIROS"]
#Reportes que no se deben subir a S3
#report_no_s3 = os.environ.get("REPORTS_NO_S3").split(',')
report_no_s3 = ["LOG-TRANSACCIONES", "MTX-TRANSACTION", "USER-BALANCES", "32A", "32B-I", "32B-II", "32B-III", "32B-IV", "32B-V", "INTEROPE-COBRAR-PDF", "INTEROPE-PAGAR-PDF"]
#Reportes que tienen fecha como condicion
apps_with_date = ["COMISIONES-BIMER" "RECARGAS","COMERCIOS", "SERVICIOS-DIRECTOS","CASHOUT", "CASHIN", "P2P", "RETIROS", "DEPOSITOS", "SERVICE-PROVIDER"]
