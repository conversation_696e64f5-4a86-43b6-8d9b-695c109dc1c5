# -*- coding: utf-8 -*-
"""
Created on Fri Jan 10 17:52:25 2025

@author: h<PERSON>nan<PERSON>
"""
import os
import pandas as pd
import logging
import boto3
from db_connection import create_connection  # Importa la función de conexión
from config import output_route, private_key_path, public_key_path, bucket_s3, apps_signed_obligatory, report_no_s3
from file_signer.file_signer import FileSigner

def export_query_to_excel(query, excel_filename, s3_key, param):
    """
    Conecta a la base de datos Oracle, ejecuta una consulta SQL y guarda el resultado en un archivo Excel.
    
    :param query: Consulta SQL a ejecutar
    :param excel_filename: Nombre del archivo Excel a guardar
    """
    # Establecer conexión con la base de datos
    connection = create_connection()
    if connection is not None:
        try:
            # Leer los resultados de la consulta en un DataFrame de pandas
            print(query)
            df = pd.read_sql(query, con=connection)
            # Se define la ruta de salida con la variable definida en el config
            excel_filename_route = f"{output_route}{excel_filename}"
            # Exportar el DataFrame a un archivo Excel
            #sheet_name = "Sheet0" if param in ["DEPOSITOS", "RETIROS"] else "Sheet1"
            #df.to_excel(excel_filename_route, index=False, sheet_name=sheet_name, engine='openpyxl')
            #logging.info("Consulta ejecutada y resultados exportados a %s", excel_filename_route)

            if param in ["DEPOSITOS", "RETIROS"]:
                #xlsx_filename = excel_filename_route.replace('.xls', '.xlsx')
                sheet_name = "Sheet0"
                df.to_excel(excel_filename_route, index=False, sheet_name=sheet_name, engine='openpyxl')
                #convert_xlsx_to_xls(xlsx_filename, excel_filename_route)
            else:
                sheet_name = "Sheet1"
                df.to_excel(excel_filename_route, index=False, sheet_name=sheet_name, engine='openpyxl')

            # Se firman los archivos
            if param in apps_signed_obligatory:
                print(private_key_path)
                signer = FileSigner(private_key_path)
                signer.sign_file(excel_filename_route)
                signature_path = f"{excel_filename_route}.signature"
                is_valid = signer.verify_signature(excel_filename_route, signature_path, public_key_path)
                filename_without_extension = excel_filename
                #signature_file = excel_filename.split('.xlsx')[0] + ".signature"
                signature_file = f"{filename_without_extension}.signature"
                if is_valid:
                    upload_s3(signature_path, signature_file, s3_key)

            # Los archivos se suben a s3 si han sido firmados correctamente
            if param not in report_no_s3:
                upload_s3(excel_filename_route, excel_filename, s3_key)

        except Exception as e:
            print(f"Error al ejecutar la consulta o exportar a Excel: {e}")
        
        finally:
            # Cerrar la conexión a la base de datos
            connection.close()
            print("Conexión cerrada.")
    
    else:
        print("No se pudo establecer conexión con la base de datos. No se ejecutó la consulta.")


def upload_s3(file_route, file_name, s3_key):
    s3_client = boto3.client('s3')
    s3_client.upload_file(file_route, bucket_s3, f"{s3_key}{file_name}")
    logging.info("Archivo: %s subido a: s3://%s", file_name, f"{bucket_s3}/{s3_key}{file_name}")



def convert_xlsx_to_xls(xlsx_filename, xls_filename):
    excel = win32com.client.Dispatch("Excel.Application")
    workbook = excel.Workbooks.Open(xlsx_filename)
    workbook.SaveAs(xls_filename, FileFormat=56)
    workbook.Close()
    excel.Quit()
