# -*- coding: utf-8 -*-
"""
Created on Fri Jan 10 17:52:53 2025

@author: h<PERSON>nan<PERSON>
"""
import sys
from export_to_excel import export_query_to_excel
from datetime import datetime, timedelta
from config import emisores, apps_with_date

def main():
    
    # Obtener el parámetro de la línea de comandos
    if len(sys.argv) < 2:
        print("Debe especificar un parámetro, como 'UNIQUE' o 'MOVISTAR'.")
        sys.exit(1)
    param = sys.argv[1].upper()  # Convertimos el parámetro a mayúsculas para asegurar consistencia

    if len(sys.argv) < 3:
        print("Debe especificar una fecha, en el formato: 'YYYY/MM/DD'")
        sys.exit(1)
    fecha = sys.argv[2].upper()

    fecha = datetime.strptime(fecha, "%Y/%m/%d")
    fecha_minus_one = fecha + timedelta(days=1)
    current_time = fecha_minus_one.strftime("%Y%m%d")
    hora_lima = datetime.utcnow() - timedelta(hours=5)
    current_time_complete_nv = hora_lima.strftime("%Y%m%d%H%M%S")
    current_time_complete = fecha_minus_one.strftime("%Y%m%d%H%M%S")
    pastdatetime = (datetime.now() - timedelta(days=31))
    year = fecha_minus_one.strftime("%Y")
    month = fecha_minus_one.strftime("%m")
    day = fecha_minus_one.strftime("%d")
    pastmonth = pastdatetime.now().strftime("%m")
    #current_time_complete = datetime.now().strftime("%Y%m%d%H%M%S")
    s3_key = f"{year}-{month}-{day}/{param}/"
    try:
        with open(f"./queries/{param}.sql", "r", encoding="utf-8") as file:
            query = file.read()
    except FileNotFoundError:
        query = ""
    query = query.replace('{fecha}', str(fecha))
    if param == "SERVICE-PROVIDER":
        excel_filename = f"PDP-REPORTE-SERVICE-PROVIDER-{current_time}.xlsx"
        export_query_to_excel(query, excel_filename, "FCOMPARTAMOS/" + s3_key, param)
        return
        
    elif param == "P2P":
        # Nombre del archivo Excel
        excel_filename = f"P2P-CF-{current_time}.xlsx"
        
    elif param == "CASHIN":
        # Nombre del archivo Excel
        excel_filename = f"Cash IN-{current_time}.xlsx"
        
    elif param == "CASHOUT":
        # Nombre del archivo Excel
        excel_filename = f"Cash Out-{current_time}.xlsx"
        
    elif param == "AGENTES-BIM":
        # Nombre del archivo Excel
        excel_filename = f"MAESTRO-BIMERS-{month}-{year}.xlsx"
        
    elif param == "COMISIONES-BIMER":
        # Nombre del archivo Excel
        excel_filename = f"COMISIONES-AGENTES-BIM-{month}-{year}.xlsx"

    elif param == "SERVICIOS-DIRECTOS":
        # Nombre del archivo Excel
        excel_filename = f"Reporte de Servicios Directos-{year}.xlsx"
    
    elif param == "BITEL-PRE":
        excel_filename = f"PDP-REPORTE-BITELPRE-{current_time_complete}.xls"

    elif param == "BITEL-POST":
        excel_filename = f"PDP-REPORTE-BITELPOST-{current_time_complete}.xls"

    elif param == "COMERCIOS":
        excel_filename = f"Reporte de recaudacion-{year}.xlsx"
        
    elif param == "RECARGAS":
        excel_filename = f"Reporte de Recargas {year} ({month}-{pastmonth}).xlsx"
        
    elif param == "DEPOSITOS":
        for emisor in emisores:
            query_e = query.replace('{emisor}', emisor)
            
            if emisor == "FCONFIANZA":
                emisor = "0231FCONFIANZA"

            excel_filename = f"DEPOSITOS-{emisor}-{current_time_complete_nv}.xls"
            print(query)
            export_query_to_excel(query_e, excel_filename, f"{emisor}/" + s3_key, param)
        return

    elif param == "RETIROS":
        for emisor in emisores:
            query_e = query.replace('{emisor}', emisor)

            if emisor == "FCONFIANZA":
                emisor = "0231FCONFIANZA"

            excel_filename = f"RETIROS-{emisor}-{current_time_complete_nv}.xls"
            print(query)
            export_query_to_excel(query_e, excel_filename, f"{emisor}/" + s3_key, param)

    elif param == "TRAZA-FEE":
        for emisor in emisores:
            query_e = query.replace('{emisor}', emisor)

            if emisor == "FCONFIANZA":
                emisor = "0231FCONFIANZA"

            excel_filename = f"REPORTE-TRAZABILIDADFEE-{emisor}-{current_time}.xlsx"
            print(query)
            export_query_to_excel(query_e, excel_filename, f"{emisor}/" + s3_key, param)
        return
        
    # Ejecutar la exportación
    if "BCRP" in param or param.startswith("BCRP"):
        export_query_to_excel(query, excel_filename, "BCRP/" + s3_key, param)
    else:
        export_query_to_excel(query, excel_filename, "PDP_INTERNO/" + s3_key, param)

if __name__ == "__main__":
    main()
