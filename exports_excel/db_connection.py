# -*- coding: utf-8 -*-
"""
Created on Fri Jan 10 17:51:32 2025

@author: h<PERSON><PERSON><PERSON>
"""

import oracledb
from db_config import connection_params  # Importamos los parámetros de conexión desde db_config.py

def create_connection():
    """
    Crea una conexión a la base de datos Oracle utilizando los parámetros configurados en db_config.py.
    
    :return: Objeto de conexión Oracle
    """
    try:
        connection = oracledb.connect(
            user=connection_params['user'],
            password=connection_params['password'],
            dsn=connection_params['dsn']
        )
        print("Conexión a Oracle establecida.")
        return connection
    
    except Exception as e:
        print(f"Error al conectar a la base de datos Oracle: {e}")
        return None
