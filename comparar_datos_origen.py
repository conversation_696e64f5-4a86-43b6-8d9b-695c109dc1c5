#!/usr/bin/env python3
import oracledb
import duckdb
import boto3

def main():
    try:
        # Conexión a Oracle
        oracle_conn = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        oracle_cursor = oracle_conn.cursor()
        
        # Configurar DuckDB con S3
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        duck_conn = duckdb.connect()
        duck_conn.sql('INSTALL httpfs;')
        duck_conn.sql('LOAD httpfs;')
        duck_conn.sql('SET s3_region=\'us-east-1\';')
        duck_conn.sql('SET s3_use_ssl=true;')
        duck_conn.sql('SET s3_url_style=\'path\';')
        duck_conn.sql(f'SET s3_access_key_id=\'{credentials.access_key}\';')
        duck_conn.sql(f'SET s3_secret_access_key=\'{credentials.secret_key}\';')
        if credentials.token:
            duck_conn.sql(f'SET s3_session_token=\'{credentials.token}\';')
        
        print("=== COMPARACIÓN DATOS ORIGEN ORACLE vs S3 ===")
        print("Fecha: 2025-06-03")
        print("=" * 60)
        
        # 1. USER_MODIFICATION_HISTORY
        print("\n1. USER_MODIFICATION_HISTORY:")
        
        # Oracle
        oracle_cursor.execute("""
            SELECT COUNT(*) 
            FROM PDP_PROD10_MAINDB.USER_MODIFICATION_HISTORY
            WHERE TRUNC(CREATED_ON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
        """)
        oracle_mod = oracle_cursor.fetchone()[0]
        print(f"   Oracle: {oracle_mod:,} registros")
        
        # S3
        try:
            result = duck_conn.sql('''
                SELECT COUNT(*) 
                FROM read_parquet("s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/USER_MODIFICATION_HISTORY_ORA/consolidado_puro.parquet")
                WHERE CAST(CREATED_ON AS DATE) = CAST("2025-06-03" AS DATE)
            ''')
            s3_mod = result.fetchone()[0]
            print(f"   S3: {s3_mod:,} registros")
            print(f"   Diferencia: {oracle_mod - s3_mod:,}")
        except Exception as e:
            print(f"   S3: Error - {e}")
        
        # 2. USER_AUTH_CHANGE_HISTORY
        print("\n2. USER_AUTH_CHANGE_HISTORY:")
        
        # Oracle
        oracle_cursor.execute("""
            SELECT COUNT(*) 
            FROM PDP_PROD10_MAINDB.USER_AUTH_CHANGE_HISTORY
            WHERE TRUNC(MODIFIED_ON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            AND AUTHENTICATION_TYPE = 'PIN'
        """)
        oracle_auth = oracle_cursor.fetchone()[0]
        print(f"   Oracle: {oracle_auth:,} registros")
        
        # S3
        try:
            result = duck_conn.sql('''
                SELECT COUNT(*) 
                FROM read_parquet("s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/USER_AUTH_CHANGE_HISTORY_ORA/consolidado_puro.parquet")
                WHERE CAST(MODIFIED_ON AS DATE) = CAST("2025-06-03" AS DATE)
                AND AUTHENTICATION_TYPE = 'PIN'
            ''')
            s3_auth = result.fetchone()[0]
            print(f"   S3: {s3_auth:,} registros")
            print(f"   Diferencia: {oracle_auth - s3_auth:,}")
        except Exception as e:
            print(f"   S3: Error - {e}")
        
        # 3. USER_PROFILE
        print("\n3. USER_PROFILE:")
        
        # Oracle
        oracle_cursor.execute("""
            SELECT COUNT(*) 
            FROM PDP_PROD10_MAINDB.USER_PROFILE
            WHERE TRUNC(CREATED_ON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
        """)
        oracle_users = oracle_cursor.fetchone()[0]
        print(f"   Oracle: {oracle_users:,} registros")
        
        # S3
        try:
            result = duck_conn.sql('''
                SELECT COUNT(*) 
                FROM read_parquet("s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/USER_PROFILE_ORA/consolidado_puro.parquet")
                WHERE CAST(CREATED_ON AS DATE) = CAST("2025-06-03" AS DATE)
            ''')
            s3_users = result.fetchone()[0]
            print(f"   S3: {s3_users:,} registros")
            print(f"   Diferencia: {oracle_users - s3_users:,}")
        except Exception as e:
            print(f"   S3: Error - {e}")
        
        # 4. Verificar REQUEST_TYPE en USER_MODIFICATION_HISTORY
        print("\n4. REQUEST_TYPE en USER_MODIFICATION_HISTORY Oracle:")
        oracle_cursor.execute("""
            SELECT REQUEST_TYPE, COUNT(*) 
            FROM PDP_PROD10_MAINDB.USER_MODIFICATION_HISTORY
            WHERE TRUNC(CREATED_ON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            GROUP BY REQUEST_TYPE
            ORDER BY COUNT(*) DESC
        """)
        oracle_types = oracle_cursor.fetchall()
        for row in oracle_types:
            print(f"   {row[0]}: {row[1]:,}")
        
        # 5. Verificar REQUEST_TYPE en S3
        print("\n5. REQUEST_TYPE en USER_MODIFICATION_HISTORY S3:")
        try:
            result = duck_conn.sql('''
                SELECT REQUEST_TYPE, COUNT(*) 
                FROM read_parquet("s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/USER_MODIFICATION_HISTORY_ORA/consolidado_puro.parquet")
                WHERE CAST(CREATED_ON AS DATE) = CAST("2025-06-03" AS DATE)
                GROUP BY REQUEST_TYPE
                ORDER BY COUNT(*) DESC
            ''')
            s3_types = result.fetchall()
            for row in s3_types:
                print(f"   {row[0]}: {row[1]:,}")
        except Exception as e:
            print(f"   Error: {e}")
        
        oracle_cursor.close()
        oracle_conn.close()
        duck_conn.close()
        
        print("\n" + "=" * 60)
        print("COMPARACIÓN COMPLETADA")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
