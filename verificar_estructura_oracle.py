#!/usr/bin/env python3
import oracledb
import sys

def main():
    try:
        # Configuración de conexión a Oracle
        connection = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        
        print("Conexión a Oracle establecida exitosamente")
        
        # Crear cursor
        cursor = connection.cursor()
        
        # Consultar la estructura de la tabla LOG_USR
        cursor.execute("""
            SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH
            FROM ALL_TAB_COLUMNS
            WHERE TABLE_NAME = 'LOG_USR'
            AND OWNER = 'USR_DATALAKE'
            ORDER BY COLUMN_ID
        """)
        
        # Obtener y mostrar resultados
        rows = cursor.fetchall()
        print(f"\nEstructura de la tabla USR_DATALAKE.LOG_USR:")
        print("=" * 80)
        print(f"{'COLUMN_NAME':<30} {'DATA_TYPE':<20} {'DATA_LENGTH':<10}")
        print("-" * 80)
        for row in rows:
            print(f"{row[0]:<30} {row[1]:<20} {row[2]:<10}")
        
        # Verificar también las tablas origen
        print(f"\n\nVerificando datos origen en Oracle para 2025-06-03:")
        print("=" * 80)
        
        # USER_MODIFICATION_HISTORY
        print("\n1. USER_MODIFICATION_HISTORY:")
        cursor.execute("""
            SELECT COUNT(*) 
            FROM PDP_PROD10_MAINDB.USER_MODIFICATION_HISTORY
            WHERE TRUNC(CREATED_ON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
        """)
        result = cursor.fetchone()
        print(f"   Registros para 2025-06-03: {result[0]:,}")
        
        # USER_AUTH_CHANGE_HISTORY
        print("\n2. USER_AUTH_CHANGE_HISTORY:")
        cursor.execute("""
            SELECT COUNT(*) 
            FROM PDP_PROD10_MAINDB.USER_AUTH_CHANGE_HISTORY
            WHERE TRUNC(MODIFIED_ON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            AND AUTHENTICATION_TYPE = 'PIN'
        """)
        result = cursor.fetchone()
        print(f"   Registros PIN para 2025-06-03: {result[0]:,}")
        
        # USER_PROFILE creados ese día
        print("\n3. USER_PROFILE:")
        cursor.execute("""
            SELECT COUNT(*) 
            FROM PDP_PROD10_MAINDB.USER_PROFILE
            WHERE TRUNC(CREATED_ON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
        """)
        result = cursor.fetchone()
        print(f"   Usuarios creados 2025-06-03: {result[0]:,}")
        
        # USER_ACCOUNT_HISTORY
        print("\n4. USER_ACCOUNT_HISTORY (MySQL):")
        print("   (Esta tabla está en MySQL, no en Oracle)")
        
        # Cerrar conexión
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
