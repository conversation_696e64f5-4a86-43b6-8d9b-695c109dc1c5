#!/usr/bin/env python3
"""
Análisis de registros con TIPODOCUMENTO vacío
Para identificar por qué tenemos 8,795 registros sin tipo de documento
"""

import pandas as pd
import duckdb

def analyze_empty_tipodocumento():
    """Analizar registros con TIPODOCUMENTO vacío en el resultado final"""
    try:
        conn = duckdb.connect()
        
        local_path = "S3_LOG_USER/output/********/LOG_USR.parquet"
        
        print("=== ANÁLISIS REGISTROS CON TIPODOCUMENTO VACÍO ===")
        
        # Contar registros vacíos por REQUESTTYPE
        query = f"""
        SELECT 
            REQUESTTYPE,
            COUNT(*) as CANTIDAD_VACIOS
        FROM read_parquet('{local_path}')
        WHERE TIPODOCUMENTO = '' OR TIPODOCUMENTO IS NULL
        GROUP BY REQUESTTYPE
        ORDER BY CANTIDAD_VACIOS DESC
        """
        
        df_vacios = conn.execute(query).df()
        print("Registros vacíos por REQUESTTYPE:")
        print(df_vacios.to_string(index=False))
        
        # Analizar algunos ejemplos de registros vacíos
        sample_query = f"""
        SELECT 
            USERHISTID,
            REQUESTTYPE,
            TIPODOCUMENTO,
            DOCUMENTO,
            BANKDOMAIN,
            USERID
        FROM read_parquet('{local_path}')
        WHERE TIPODOCUMENTO = '' OR TIPODOCUMENTO IS NULL
        LIMIT 10
        """
        
        df_sample = conn.execute(sample_query).df()
        print(f"\nEjemplos de registros con TIPODOCUMENTO vacío:")
        print(df_sample.to_string(index=False))
        
        return df_vacios
        
    except Exception as e:
        print(f"Error analizando registros vacíos: {e}")
        return None

def analyze_user_data_trx_kyc():
    """Analizar el JOIN entre USER_DATA_TRX y KYC_DETAILS"""
    try:
        conn = duckdb.connect()
        
        user_data_path = "S3_LOG_USER/TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet"
        
        print("\n=== ANÁLISIS USER_DATA_TRX - KYC MAPPING ===")
        
        # Verificar cuántos usuarios tienen KYC_ID
        query = f"""
        SELECT 
            COUNT(*) as TOTAL_USUARIOS,
            COUNT(CASE WHEN ID_TYPE IS NOT NULL AND ID_TYPE != '' THEN 1 END) as CON_ID_TYPE,
            COUNT(CASE WHEN ID_TYPE IS NULL OR ID_TYPE = '' THEN 1 END) as SIN_ID_TYPE
        FROM read_parquet('{user_data_path}')
        """
        
        df_stats = conn.execute(query).df()
        print("Estadísticas USER_DATA_TRX:")
        print(df_stats.to_string(index=False))
        
        # Verificar algunos ejemplos sin ID_TYPE
        sample_query = f"""
        SELECT 
            O_USER_ID,
            ID_TYPE,
            ID_VALUE,
            PROFILE,
            ISSUER_CODE,
            CREATED_ON
        FROM read_parquet('{user_data_path}')
        WHERE ID_TYPE IS NULL OR ID_TYPE = ''
        LIMIT 10
        """
        
        df_sample = conn.execute(sample_query).df()
        print(f"\nEjemplos de usuarios sin ID_TYPE en USER_DATA_TRX:")
        print(df_sample.to_string(index=False))
        
        return df_stats
        
    except Exception as e:
        print(f"Error analizando USER_DATA_TRX: {e}")
        return None

def analyze_kyc_coverage():
    """Analizar cobertura de KYC_DETAILS vs USER_PROFILE"""
    try:
        import boto3
        
        session = boto3.Session()
        conn = duckdb.connect()
        conn.execute("INSTALL httpfs;")
        conn.execute("LOAD httpfs;")
        
        credentials = session.get_credentials()
        conn.execute(f"""
        SET s3_region='us-east-1';
        SET s3_access_key_id='{credentials.access_key}';
        SET s3_secret_access_key='{credentials.secret_key}';
        """)
        
        if credentials.token:
            conn.execute(f"SET s3_session_token='{credentials.token}';")
        
        print("\n=== ANÁLISIS COBERTURA KYC_DETAILS ===")
        
        user_profile_path = "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/USER_PROFILE_ORA/consolidado_puro.parquet"
        kyc_details_path = "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/KYC_DETAILS_ORA/consolidado_puro.parquet"
        
        # Verificar cobertura KYC
        query = f"""
        WITH USER_STATS AS (
            SELECT 
                COUNT(*) as TOTAL_USERS,
                COUNT(KYC_ID) as USERS_WITH_KYC_ID
            FROM read_parquet('{user_profile_path}')
            WHERE CREATED_ON <= '2025-06-03'
        ),
        KYC_STATS AS (
            SELECT 
                COUNT(DISTINCT KYC_ID) as TOTAL_KYC_RECORDS,
                COUNT(DISTINCT CASE WHEN STATUS = 'A' THEN KYC_ID END) as ACTIVE_KYC_RECORDS
            FROM read_parquet('{kyc_details_path}')
        )
        SELECT 
            US.TOTAL_USERS,
            US.USERS_WITH_KYC_ID,
            KS.TOTAL_KYC_RECORDS,
            KS.ACTIVE_KYC_RECORDS,
            ROUND(100.0 * US.USERS_WITH_KYC_ID / US.TOTAL_USERS, 2) as PORCENTAJE_CON_KYC
        FROM USER_STATS US, KYC_STATS KS
        """
        
        df_coverage = conn.execute(query).df()
        print("Cobertura KYC:")
        print(df_coverage.to_string(index=False))
        
        # Verificar algunos usuarios sin KYC
        sample_query = f"""
        SELECT 
            UP.USER_ID,
            UP.KYC_ID,
            UP.USER_CODE,
            UP.PROFILE,
            UP.CREATED_ON
        FROM read_parquet('{user_profile_path}') UP
        WHERE (UP.KYC_ID IS NULL OR UP.KYC_ID = '')
        AND UP.CREATED_ON <= '2025-06-03'
        LIMIT 10
        """
        
        df_no_kyc = conn.execute(sample_query).df()
        print(f"\nEjemplos de usuarios sin KYC_ID:")
        print(df_no_kyc.to_string(index=False))
        
        return df_coverage
        
    except Exception as e:
        print(f"Error analizando cobertura KYC: {e}")
        return None

def main():
    """Función principal"""
    print("Iniciando análisis de TIPODOCUMENTO vacíos")
    print("="*60)
    
    # Analizar registros vacíos en resultado final
    analyze_empty_tipodocumento()
    
    # Analizar USER_DATA_TRX
    analyze_user_data_trx_kyc()
    
    # Analizar cobertura KYC
    analyze_kyc_coverage()
    
    print("\n" + "="*60)
    print("CONCLUSIÓN:")
    print("Identificar por qué hay usuarios sin TIPODOCUMENTO")
    print("y cómo Oracle maneja estos casos")

if __name__ == "__main__":
    main()
