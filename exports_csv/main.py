# src/main.py

import sys
from infrastructure.adapters.oracle_adapter import OracleAdapter
from core.use_cases.export_sql_to_csv import ExportSqlToCsv
from datetime import datetime, timedelta
from config import apps_with_date, emisores

def main():
    # Obtener el parámetro de la línea de comandos
    if len(sys.argv) < 2:
        print("Debe especificar un parámetro, como 'UNIQUE' o 'MOVISTAR'.")
        sys.exit(1)

    param = sys.argv[1].upper()  # Convertimos el parámetro a mayúsculas para asegurar consistencia
    #emisor = sys.argv[2].upper()
    if len(sys.argv) < 3:
        print("Debe especificar una fecha, en el formato: 'YYYY/MM/DD'")
        sys.exit(1)
    fecha = sys.argv[2].upper()
    fecha = datetime.strptime(fecha, "%Y/%m/%d")
    fecha_minus_one = fecha + timedelta(days=1)
    current_time = fecha_minus_one.strftime("%Y%m%d")
    current_month = fecha_minus_one.strftime("%Y%m")
    current_time_complete = fecha_minus_one.strftime("%Y%m%d%H%M%S")
    
    # Instanciar el adaptador de Oracle y el caso de uso de exportación a CSV
    oracle_adapter = OracleAdapter()
    # oracle_adapter = ''
    year = fecha_minus_one.strftime("%Y")
    month = fecha_minus_one.strftime("%m")
    day = fecha_minus_one.strftime("%d")
    s3_key = f"{year}-{month}-{day}/{param}/"
    export_sql_to_csv = ExportSqlToCsv(oracle_adapter)
    try:
        with open(f"./queries/{param}.sql", "r", encoding="utf-8") as file:
            query = file.read()
    except FileNotFoundError:
        query = ""
    query = query.replace('{fecha}', str(fecha))
    # Definir las consultas SQL según el parámetro
    if param == "TRAZA-FEE":
        file_name = f"REPORTE-TRAZABILIDADFEE-{current_month}.csv"
    
    elif param == "MOVISTAR":
        file_name = f"PDP-REPORTE-MOVISTAR-{current_time_complete}.csv"
        
    elif param == "UNIQUE":
        file_name = f"PDP-REPORTE-UNIQUE-{current_time_complete}.csv"
    
    elif param == "ENTEL":
        file_name = f"PDP-REPORTE-ENTELTDE-{current_time}.csv"

    elif param == "CRANDES-PAGOS":
        file_name = f"PDP-REPORTE-CRANDESPAGOLINEA-{current_time_complete}.csv"
    
    elif param == "RETIRO-SENTINEL":
        file_name = f"PDP-RETIROS-SENTINEL-{current_time}.csv"
        
    elif param == "RETIRO-WU-HUB":
        file_name = f"PDP-RETIROS-WU_HUB-{current_time}.csv"
        
    elif param == "QR-NIUBIZ":
        file_name = f"PDP-REPORTE-BACKUS-{current_time}.csv"
    
    elif param == "QR-IZIPAY":
        file_name = f"PDP-REPORTE-PMP-{current_time}.csv"

    elif param == "AZULITO":
        file_name = f"PDP-REPORTE-AZULITO-{current_time}.csv"
    
    elif param == "VALIDAR-BALANCE":
        file_name = f"PDP-VALIDAR-BALANCE-{current_time}.csv"

    elif param == "LOG-TRANSACCIONES":
        file_name = f"TR-{current_time}.csv"

    elif param == "MTX-TRANSACTION":
        file_name = f"MTX_TRANSACTION_HEADER_{current_time}.csv"

    elif param == "LOG-USUARIOS":
        file_name = f"data-{current_time}.csv"

    elif param == "BCRP-BALANCES":
        file_name = f"user-account-balances-{current_time}_000000.csv"
    
    elif param == "BCRP-TIPO-CUENTAS":
        file_name = f"BCRP-TIPO_CUENTAS_DINERO_ELECTRONICO-{current_time_complete}.csv"
    
    elif param == "BCRP-NETO-EMISORES":
        file_name = f"BCRP-NETO_PAGAR_ENTRE_EMISORES-{current_time_complete}.csv"
    
    elif param == "BCRP-OPERACIONES-EMISOR":
        file_name = f"BCRP-OPERACIONES_POR_EMISOR-{current_time_complete}.csv"

    elif param == "32A":
        file_name = f"32A-{current_time}.csv"

    elif param == "32B-I":
        file_name = f"32B-I-{current_time}.csv"

    elif param == "32B-II":
        file_name = f"32B-II-{current_time}.csv"

    elif param == "32B-IV":
        file_name = f"32B-IV-{current_time}.csv"

    elif param == "32B-V":
        file_name = f"32B-V-{current_time}.csv"

    elif param == "32B-III":
        file_name = f"32B-III-{current_time}.csv"

    elif param == "COMISIONES":
        for emisor in emisores:
            query_e = query.replace('{emisor}', emisor)
            
            if emisor == "FCONFIANZA":
                emisor = "0231FCONFIANZA"

            file_name = f"COMISIONES-{emisor}-{current_time}.csv"
            export_sql_to_csv.execute(query_e, file_name, f"{emisor}/" + s3_key, param)
        return

    elif param == "INTEROPE-COBRAR":
        for emisor in emisores:
            query_e = query.replace('{emisor}', emisor)
            
            if emisor == "FCONFIANZA":
                emisor = "0231FCONFIANZA"

            file_name = f"INTEROPE-{emisor}-COBRAR-{current_month}.csv"
            export_sql_to_csv.execute(query_e, file_name, f"{emisor}/" + s3_key, param)
        return
    
    elif param == "INTEROPE-PAGAR":
        for emisor in emisores:
            print(emisor)
            query_e = query.replace('{emisor}', emisor)
            
            if emisor == "FCONFIANZA":
                emisor = "0231FCONFIANZA"

            file_name = f"INTEROPE-{emisor}-PAGAR-{current_month}.csv"
            export_sql_to_csv.execute(query_e, file_name, f"{emisor}/" + s3_key, param)
        return
    
    elif param == "INTEROPE-COBRAR-PDF":
        for emisor in emisores:
            query_e = query.replace('{emisor}', emisor)
            
            if emisor == "FCONFIANZA":
                emisor = "0231FCONFIANZA"
            
            file_name = f"INTEROPE-{emisor}-COBRAR-{current_month}-PDF.csv"
            export_sql_to_csv.execute(query_e, file_name, f"{emisor}/" + s3_key, param)
        return

    elif param == "INTEROPE-PAGAR-PDF":
        for emisor in emisores:
            print(emisor)
            query_e = query.replace('{emisor}', emisor)
            
            if emisor == "FCONFIANZA":
                emisor = "0231FCONFIANZA"

            file_name = f"INTEROPE-{emisor}-PAGAR-{current_month}-PDF.csv"
            export_sql_to_csv.execute(query_e, file_name, f"{emisor}/" + s3_key, param)
        return

    elif param == "EQUIVALENCIA-AHORROS":
        file_name = f"PDP-EQUIVALENCIA-AHORROS-{current_time}.csv"
        export_sql_to_csv.execute(query, file_name, f"FCOMPARTAMOS/" + s3_key, param)
        return

    elif param == "EQUIVALENCIA-PAGOS":
        file_name = f"PDP-EQUIVALENCIA-PAGOS-{current_time}.csv"
        export_sql_to_csv.execute(query, file_name, f"FCOMPARTAMOS/" + s3_key, param)
        return

    elif param == "EQUIVALENCIA-LOG-TRX":
        file_name = f"PDP-EQUIVALENCIA-LOG-TRX-{current_time}.csv"
        export_sql_to_csv.execute(query, file_name, f"FCOMPARTAMOS/" + s3_key, param)
        return

    elif param == "Q1":
        file_name = "Q1.csv"
     
    elif param == "Q2":
        file_name = "Q2.csv"
    
    elif param == "Q3":
        file_name = "Q3.csv"
    
    elif param == "Q4":
        file_name = "Q4.csv"
    
    elif param == "Q5":
        file_name = "Q5.csv"
    
    elif param == "Q6":
        file_name = "Q6.csv"
    
    else:
        print("Parámetro no reconocido.")
        sys.exit(1)

    # Ejecutar la exportación de la consulta SQL a CSV
    if "BCRP" in param or param.startswith("BCRP"):
        export_sql_to_csv.execute(query, file_name, "BCRP/" + s3_key, param)
    else:
        export_sql_to_csv.execute(query, file_name, "PDP_INTERNO/" + s3_key, param)

if __name__ == "__main__":
    main()
