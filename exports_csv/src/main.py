# src/main.py

from infrastructure.adapters.oracle_adapter import OracleAdapter
from core.use_cases.export_sql_to_csv import ExportSqlToCsv

def main():
    # Instanciar el adaptador de Oracle y el caso de uso de exportación a CSV
    oracle_adapter = OracleAdapter()
    export_sql_to_csv = ExportSqlToCsv(oracle_adapter)
    
    # Definir la consulta SQL
    query = """
    SELECT
        MTH.ATTR_3_VALUE AS trx_id,
        MTH.TRANSFER_ID AS financial_trx_id,
        MTH.REFERENCE_NUMBER AS external_trx_id,
        TO_CHAR(MTH."TRANSFER_DATE", 'YYYY-MM-DD HH24:MI:SS') as datetime,
        UP2.MSISDN AS msisdn,
        REPLACE(MTH.REMARKS,'_','@') AS complete_username,
        MTH.TRANSFER_VALUE/100 AS monto
    FROM PDP_SITX10_MAINDBBUS.MTX_TRANSACTION_HEADER mth
    LEFT JOIN PDP_SITX10_MAINDB.USER_PROFILE up
    ON MTH.PAYEE_USER_ID = UP.USER_ID
    LEFT JOIN PDP_SITX10_MAINDB.USER_PROFILE up2
    ON MTH.PAYER_USER_ID = UP2.USER_ID
    WHERE 1=1
    AND MTH.SERVICE_TYPE = 'BILLPAY'
    AND UP.LOGIN_ID = 'unique1'
    """
    
    # Ejecutar la exportación de la consulta SQL a CSV
    export_sql_to_csv.execute(query, 'reporte.csv')

if __name__ == "__main__":
    main()
