# src/infrastructure/adapters/oracle_adapter.py

import oracledb
import pandas as pd
from src.core.models.report_row import ReportRow
from src.core.interfaces.database_repository import DatabaseRepository
from typing import List
from config.database_config import DATABASE_CONFIG

class OracleAdapter(DatabaseRepository):
    def __init__(self):
        # Configuración de conexión
        self.username = DATABASE_CONFIG['username']
        self.password = DATABASE_CONFIG['password']
        self.dsn = DATABASE_CONFIG['dsn']

    def get_data(self, query: str) -> List[ReportRow]:
        """Ejecuta una consulta SQL en Oracle usando oracledb y devuelve los resultados como una lista de ReportRow."""
        connection = None
        cursor = None
        try:
            # Conectar a la base de datos Oracle usando oracledb
            connection = oracledb.connect(
                user=self.username,
                password=self.password,
                dsn=self.dsn
            )
            cursor = connection.cursor()

            # Ejecutar la consulta y obtener los resultados
            cursor.execute(query)
            rows = cursor.fetchall()

            # Verificar si se obtuvieron registros
            if not rows:
                print("No se encontraron registros en la consulta.")
                return []  # Retorna una lista vacía si no hay registros

            # Obtener los nombres de las columnas
            columns = [desc[0] for desc in cursor.description]

            # Crear un DataFrame de pandas con los resultados
            df = pd.DataFrame(rows, columns=columns)

            # Si el DataFrame está vacío, retornamos una lista vacía
            if df.empty:
                print("El DataFrame está vacío después de ejecutar la consulta.")
                return []

            # Convertir el DataFrame a una lista de ReportRow
            report_rows = [ReportRow(row.to_dict()) for _, row in df.iterrows()]
            return report_rows

        except oracledb.Error as error:
            # Capturamos los errores específicos de Oracle
            print(f"Error al ejecutar la consulta en Oracle: {error}")
            return []  

        except Exception as error:
            # Capturamos cualquier otro error general
            print(f"Error general: {error}")
            return []  

        finally:
            # Cerrar el cursor y la conexión en el bloque finally para asegurar su cierre
            if cursor:
                cursor.close()
            if connection:
                connection.close()
