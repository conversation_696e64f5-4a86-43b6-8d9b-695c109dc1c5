# src/core/use_cases/export_sql_to_csv.py

import os
import pandas as pd

class ExportSqlToCsv:
    def __init__(self, oracle_adapter):
        self.oracle_adapter = oracle_adapter

    def execute(self, query: str, file_name: str):
        # Define la ruta para guardar el archivo en el escritorio
        desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop', file_name)
        
        # Obtener los datos desde Oracle a través del adaptador
        data = self.oracle_adapter.get_data(query)
        
        # Convertir los datos a un DataFrame
        df = pd.DataFrame([row.to_dict() for row in data])
        
        # Verificar si el DataFrame contiene datos
        if df.empty:
            print("No se encontraron datos para exportar.")
        else:
            print("Datos encontrados. Procediendo a exportar el archivo CSV.")
        
        # Exportar el DataFrame a un archivo CSV
        df.to_csv(desktop_path, index=False)
        
        print(f"Archivo CSV exportado a {desktop_path}")

