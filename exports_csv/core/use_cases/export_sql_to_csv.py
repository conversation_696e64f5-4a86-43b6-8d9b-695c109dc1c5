# src/core/use_cases/export_sql_to_csv.py

import os
import pandas as pd
import logging
import csv
from config import private_key_path, public_key_path, bucket_s3, apps_signed_obligatory, report_no_s3, output_route_csv
from file_signer.file_signer import FileSigner
import boto3
class ExportSqlToCsv:
    def __init__(self, oracle_adapter):
        self.oracle_adapter = oracle_adapter

    def execute(self, query: str, file_name: str, s3_key: str, param: str):
        # Define la ruta para la carpeta 'REPORTES DRYRUN' en el escritorio
        
        report_folder = output_route_csv if output_route_csv else os.path.join(os.path.expanduser('~'), 'Desktop', 'REPORTES DRYRUN')
        # Verificar si la carpeta 'REPORTES DRYRUN' existe, si no, crearla
        if not os.path.exists(report_folder):
            os.makedirs(report_folder)
        
        # Define la ruta completa para guardar el archivo CSV dentro de 'REPORTES DRYRUN'
        desktop_path = os.path.join(report_folder, file_name)
        
        print(query)

        # Obtener los datos desde Oracle a través del adaptador
        data = self.oracle_adapter.get_data(query)
        
        print(data)
        # Convertir los datos a un DataFrame
        df = pd.DataFrame([row.to_dict() for row in data])
        
        #print(df.head())
        
        # Verificar si el DataFrame contiene datos
        if df.empty:
            logging.info("No se encontraron datos para exportar.")
        else:
            logging.info("Datos encontrados. Procediendo a exportar el archivo CSV.")
        
        # Exportar el DataFrame a un archivo CSV
        if "BCRP" in param or param.startswith("BCRP"):
            df.to_csv(desktop_path, index=False, header=True)
        elif param not in report_no_s3:
            df.to_csv(desktop_path, index=False, header=False)
        else:
            df.to_csv(desktop_path, index=False, header=True)

        if param == "UNIQUE" or param == "BCRP-OPERACIONES-EMISOR" or param == "MOVISTAR" or param == "ENTEL" or param == "CRANDES-PAGOS":
            with open(desktop_path, 'r', encoding='utf-8') as file:
                file_content = file.read()

            file_content = file_content.replace('"', '')

            with open(desktop_path, 'w', encoding='utf-8') as file:
                file.write(file_content)

            print(f"Archivo guardado correctamente en {desktop_path}")


        logging.info(f"Archivo CSV exportado a {desktop_path}")
        # Se firman los archivos
        if param in apps_signed_obligatory:
            signer = FileSigner(private_key_path)
            signer.sign_file(desktop_path)
            signature_path = f"{desktop_path}.signature"
            is_valid = signer.verify_signature(desktop_path, signature_path, public_key_path)
            signature_file = file_name + ".signature"
            if is_valid:
                upload_s3(signature_path, signature_file, s3_key)
        # Los archivos se suben a s3 si han sido firmados correctamente
        if param not in report_no_s3:
            upload_s3(desktop_path, file_name, s3_key)

def upload_s3(file_route, file_name, s3_key):
    s3_client = boto3.client('s3')
    s3_client.upload_file(file_route, bucket_s3, f"{s3_key}{file_name}")
    logging.info("Archivo: %s subido a: s3://%s", file_name, f"{bucket_s3}/{s3_key}{file_name}")

