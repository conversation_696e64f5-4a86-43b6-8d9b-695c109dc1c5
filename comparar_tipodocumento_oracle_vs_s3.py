#!/usr/bin/env python3
"""
Comparación DISTINCT TIPODOCUMENTO: Oracle vs Nuestro Pipeline S3
Data Engineering: Análisis exhaustivo de diferencias
"""

import oracledb
import duckdb
import boto3

def main():
    try:
        print("=== COMPARACIÓN DISTINCT TIPODOCUMENTO - DATA ENGINEERING ===")
        print("Oracle LOG_USR vs Pipeline S3 - Fecha: 2025-06-03")
        print("=" * 80)
        
        # 1. ANÁLISIS ORACLE
        print("\n1. ANÁLISIS ORACLE LOG_USR:")
        print("-" * 50)
        
        oracle_conn = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='*************:1521/MMONEY'
        )
        oracle_cursor = oracle_conn.cursor()
        
        # DISTINCT TIPODOCUMENTO en Oracle
        oracle_cursor.execute("""
            SELECT 
                CASE 
                    WHEN TIPODOCUMENTO IS NULL THEN '[NULL]'
                    WHEN TIPODOCUMENTO = '' THEN '[EMPTY]'
                    ELSE TIPODOCUMENTO
                END as tipodoc_display,
                TIPODOCUMENTO as tipodoc_real,
                COUNT(*) as cantidad,
                ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as porcentaje
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            GROUP BY TIPODOCUMENTO
            ORDER BY COUNT(*) DESC
        """)
        oracle_results = oracle_cursor.fetchall()
        
        print("ORACLE TIPODOCUMENTO:")
        print("VALOR           | CANTIDAD | PORCENTAJE")
        print("-" * 45)
        oracle_total = 0
        oracle_dict = {}
        
        for row in oracle_results:
            display_val = row[0]
            real_val = row[1]
            cantidad = row[2]
            porcentaje = row[3]
            oracle_total += cantidad
            oracle_dict[real_val] = cantidad
            
            print(f"{display_val:<15} | {cantidad:>8,} | {porcentaje:>8.2f}%")
        
        print(f"\nTotal Oracle: {oracle_total:,} registros")
        
        oracle_cursor.close()
        oracle_conn.close()
        
        # 2. ANÁLISIS NUESTRO PIPELINE S3
        print("\n2. ANÁLISIS NUESTRO PIPELINE S3:")
        print("-" * 50)
        
        # Configurar DuckDB
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        duck_conn = duckdb.connect()
        duck_conn.sql('INSTALL httpfs;')
        duck_conn.sql('LOAD httpfs;')
        duck_conn.sql('SET s3_region=\'us-east-1\';')
        duck_conn.sql('SET s3_use_ssl=true;')
        duck_conn.sql('SET s3_url_style=\'path\';')
        duck_conn.sql(f'SET s3_access_key_id=\'{credentials.access_key}\';')
        duck_conn.sql(f'SET s3_secret_access_key=\'{credentials.secret_key}\';')
        if credentials.token:
            duck_conn.sql(f'SET s3_session_token=\'{credentials.token}\';')
        
        # DISTINCT TIPODOCUMENTO en nuestro pipeline
        try:
            result = duck_conn.sql('''
                SELECT 
                    CASE 
                        WHEN TIPODOCUMENTO IS NULL THEN '[NULL]'
                        WHEN TIPODOCUMENTO = '' THEN '[EMPTY]'
                        ELSE TIPODOCUMENTO
                    END as tipodoc_display,
                    TIPODOCUMENTO as tipodoc_real,
                    COUNT(*) as cantidad,
                    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as porcentaje
                FROM read_parquet("output/20250603/LOG_USR.parquet")
                GROUP BY TIPODOCUMENTO
                ORDER BY COUNT(*) DESC
            ''')
            s3_results = result.fetchall()
            
            print("NUESTRO S3 TIPODOCUMENTO:")
            print("VALOR           | CANTIDAD | PORCENTAJE")
            print("-" * 45)
            s3_total = 0
            s3_dict = {}
            
            for row in s3_results:
                display_val = row[0]
                real_val = row[1]
                cantidad = row[2]
                porcentaje = row[3]
                s3_total += cantidad
                s3_dict[real_val] = cantidad
                
                print(f"{display_val:<15} | {cantidad:>8,} | {porcentaje:>8.2f}%")
            
            print(f"\nTotal S3: {s3_total:,} registros")
            
        except Exception as e:
            print(f"Error leyendo S3: {e}")
            s3_dict = {}
            s3_total = 0
        
        # 3. COMPARACIÓN DETALLADA
        print("\n3. COMPARACIÓN DETALLADA:")
        print("=" * 80)
        
        # Obtener todos los valores únicos
        all_values = set(oracle_dict.keys()) | set(s3_dict.keys())
        
        print("VALOR           | ORACLE   | S3       | DIFERENCIA | STATUS")
        print("-" * 65)
        
        total_diff = 0
        issues = []
        
        for value in sorted(all_values, key=lambda x: oracle_dict.get(x, 0), reverse=True):
            oracle_count = oracle_dict.get(value, 0)
            s3_count = s3_dict.get(value, 0)
            diff = s3_count - oracle_count
            total_diff += abs(diff)
            
            # Determinar status
            if diff == 0:
                status = "✅ OK"
            elif oracle_count == 0:
                status = "⚠️ EXTRA"
                issues.append(f"Valor '{value}' existe en S3 pero no en Oracle")
            elif s3_count == 0:
                status = "❌ FALTA"
                issues.append(f"Valor '{value}' existe en Oracle pero no en S3")
            else:
                status = "⚠️ DIFF"
                issues.append(f"Valor '{value}': Oracle {oracle_count:,} vs S3 {s3_count:,}")
            
            display_val = '[NULL]' if value is None else ('[EMPTY]' if value == '' else value)
            print(f"{display_val:<15} | {oracle_count:>8,} | {s3_count:>8,} | {diff:>+9,} | {status}")
        
        # 4. RESUMEN DE DIFERENCIAS
        print("\n4. RESUMEN DE DIFERENCIAS:")
        print("=" * 80)
        
        print(f"Total registros Oracle: {oracle_total:,}")
        print(f"Total registros S3: {s3_total:,}")
        print(f"Diferencia total: {s3_total - oracle_total:+,}")
        print(f"Diferencia absoluta: {total_diff:,}")
        
        if not issues:
            print("\n🎉 PERFECTO: Los valores TIPODOCUMENTO son IDÉNTICOS")
            print("✅ Distribución exacta entre Oracle y S3")
        else:
            print(f"\n⚠️ PROBLEMAS ENCONTRADOS: {len(issues)}")
            for i, issue in enumerate(issues, 1):
                print(f"   {i}. {issue}")
        
        # 5. ANÁLISIS DE CAUSA RAÍZ
        print("\n5. ANÁLISIS DE CAUSA RAÍZ:")
        print("=" * 80)
        
        if issues:
            print("POSIBLES CAUSAS:")
            print("1. Problema en JOINs entre tablas S3")
            print("2. Datos faltantes en tablas S3 origen")
            print("3. Lógica de transformación incorrecta")
            print("4. Filtros de fecha diferentes")
            
            print("\nACCIONES RECOMENDADAS:")
            print("1. Verificar datos en tablas S3 origen")
            print("2. Revisar JOINs en el pipeline")
            print("3. Comparar filtros de fecha")
            print("4. Validar lógica de transformación")
        else:
            print("✅ NO SE REQUIEREN ACCIONES")
            print("Los datos TIPODOCUMENTO son exactamente iguales")
        
        # 6. MUESTRA DE REGISTROS PROBLEMÁTICOS
        if issues and s3_total > 0:
            print("\n6. MUESTRA DE REGISTROS:")
            print("-" * 50)
            
            try:
                result = duck_conn.sql('''
                    SELECT USERHISTID, TIPODOCUMENTO, DOCUMENTO, REQUESTTYPE
                    FROM read_parquet("output/20250603/LOG_USR.parquet")
                    ORDER BY CREATEDON
                    LIMIT 5
                ''')
                sample = result.fetchall()
                
                print("USERHISTID | TIPODOC | DOCUMENTO | REQUESTTYPE")
                print("-" * 55)
                for row in sample:
                    print(f"{row[0]:<12} | {row[1]:<7} | {row[2]:<15} | {row[3]}")
                    
            except Exception as e:
                print(f"Error obteniendo muestra: {e}")
        
        duck_conn.close()
        
        print("\n" + "=" * 80)
        print("ANÁLISIS DISTINCT COMPLETADO")
        print("=" * 80)
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
