#!/usr/bin/env python3
import oracledb
import duckdb
import boto3

def main():
    try:
        # Conexión a Oracle
        oracle_conn = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='10.240.131.10:1521/MMONEY'
        )
        oracle_cursor = oracle_conn.cursor()
        
        # Configurar DuckDB con S3
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        duck_conn = duckdb.connect()
        duck_conn.sql('INSTALL httpfs;')
        duck_conn.sql('LOAD httpfs;')
        duck_conn.sql('SET s3_region=\'us-east-1\';')
        duck_conn.sql('SET s3_use_ssl=true;')
        duck_conn.sql('SET s3_url_style=\'path\';')
        duck_conn.sql(f'SET s3_access_key_id=\'{credentials.access_key}\';')
        duck_conn.sql(f'SET s3_secret_access_key=\'{credentials.secret_key}\';')
        if credentials.token:
            duck_conn.sql(f'SET s3_session_token=\'{credentials.token}\';')
        
        print("=== ANÁLISIS TABLAS ORIGEN: ORACLE vs S3 ===")
        print("Fecha: 2025-06-03")
        print("=" * 70)
        
        # 1. USER_MODIFICATION_HISTORY
        print("\n1. USER_MODIFICATION_HISTORY:")
        print("-" * 50)
        
        # Oracle
        oracle_cursor.execute("""
            SELECT COUNT(*) 
            FROM PDP_PROD10_MAINDB.USER_MODIFICATION_HISTORY
            WHERE TRUNC(CREATED_ON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
        """)
        oracle_mod = oracle_cursor.fetchone()[0]
        print(f"   Oracle: {oracle_mod:,} registros")
        
        # REQUEST_TYPE en Oracle
        oracle_cursor.execute("""
            SELECT REQUEST_TYPE, COUNT(*) 
            FROM PDP_PROD10_MAINDB.USER_MODIFICATION_HISTORY
            WHERE TRUNC(CREATED_ON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            GROUP BY REQUEST_TYPE
            ORDER BY COUNT(*) DESC
        """)
        oracle_types = oracle_cursor.fetchall()
        print("   Oracle REQUEST_TYPE:")
        for row in oracle_types:
            print(f"     - {row[0]}: {row[1]:,}")
        
        # S3
        try:
            result = duck_conn.sql('''
                SELECT COUNT(*)
                FROM read_parquet("s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/USER_MODIFICATION_HISTORY_ORA/consolidado_puro.parquet")
                WHERE CAST(CREATED_ON AS DATE) = CAST('2025-06-03' AS DATE)
            ''')
            s3_mod = result.fetchone()[0]
            print(f"   S3: {s3_mod:,} registros")

            # REQUEST_TYPE en S3
            result = duck_conn.sql('''
                SELECT REQUEST_TYPE, COUNT(*)
                FROM read_parquet("s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/USER_MODIFICATION_HISTORY_ORA/consolidado_puro.parquet")
                WHERE CAST(CREATED_ON AS DATE) = CAST('2025-06-03' AS DATE)
                GROUP BY REQUEST_TYPE
                ORDER BY COUNT(*) DESC
            ''')
            s3_types = result.fetchall()
            print("   S3 REQUEST_TYPE:")
            for row in s3_types:
                print(f"     - {row[0]}: {row[1]:,}")

            print(f"   DIFERENCIA: {oracle_mod - s3_mod:,} registros")
            
        except Exception as e:
            print(f"   S3 Error: {e}")
        
        # 2. USER_AUTH_CHANGE_HISTORY
        print("\n2. USER_AUTH_CHANGE_HISTORY:")
        print("-" * 50)
        
        # Oracle
        oracle_cursor.execute("""
            SELECT COUNT(*) 
            FROM PDP_PROD10_MAINDB.USER_AUTH_CHANGE_HISTORY
            WHERE TRUNC(MODIFIED_ON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            AND AUTHENTICATION_TYPE = 'PIN'
        """)
        oracle_auth = oracle_cursor.fetchone()[0]
        print(f"   Oracle (PIN): {oracle_auth:,} registros")
        
        # Tipos de modificación en Oracle
        oracle_cursor.execute("""
            SELECT MODIFICATION_TYPE, COUNT(*) 
            FROM PDP_PROD10_MAINDB.USER_AUTH_CHANGE_HISTORY
            WHERE TRUNC(MODIFIED_ON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            AND AUTHENTICATION_TYPE = 'PIN'
            GROUP BY MODIFICATION_TYPE
            ORDER BY COUNT(*) DESC
        """)
        oracle_mod_types = oracle_cursor.fetchall()
        print("   Oracle MODIFICATION_TYPE:")
        for row in oracle_mod_types:
            print(f"     - {row[0]}: {row[1]:,}")
        
        # S3
        try:
            result = duck_conn.sql('''
                SELECT COUNT(*)
                FROM read_parquet("s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/USER_AUTH_CHANGE_HISTORY_ORA/consolidado_puro.parquet")
                WHERE CAST(MODIFIED_ON AS DATE) = CAST('2025-06-03' AS DATE)
                AND AUTHENTICATION_TYPE = 'PIN'
            ''')
            s3_auth = result.fetchone()[0]
            print(f"   S3 (PIN): {s3_auth:,} registros")

            # Tipos de modificación en S3
            result = duck_conn.sql('''
                SELECT MODIFICATION_TYPE, COUNT(*)
                FROM read_parquet("s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/USER_AUTH_CHANGE_HISTORY_ORA/consolidado_puro.parquet")
                WHERE CAST(MODIFIED_ON AS DATE) = CAST('2025-06-03' AS DATE)
                AND AUTHENTICATION_TYPE = 'PIN'
                GROUP BY MODIFICATION_TYPE
                ORDER BY COUNT(*) DESC
            ''')
            s3_mod_types = result.fetchall()
            print("   S3 MODIFICATION_TYPE:")
            for row in s3_mod_types:
                print(f"     - {row[0]}: {row[1]:,}")

            print(f"   DIFERENCIA: {oracle_auth - s3_auth:,} registros")
            
        except Exception as e:
            print(f"   S3 Error: {e}")
        
        # 3. USER_PROFILE
        print("\n3. USER_PROFILE:")
        print("-" * 50)
        
        # Oracle
        oracle_cursor.execute("""
            SELECT COUNT(*) 
            FROM PDP_PROD10_MAINDB.USER_PROFILE
            WHERE TRUNC(CREATED_ON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
        """)
        oracle_users = oracle_cursor.fetchone()[0]
        print(f"   Oracle: {oracle_users:,} registros")
        
        # S3
        try:
            result = duck_conn.sql('''
                SELECT COUNT(*)
                FROM read_parquet("s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/USER_PROFILE_ORA/consolidado_puro.parquet")
                WHERE CAST(CREATED_ON AS DATE) = CAST('2025-06-03' AS DATE)
            ''')
            s3_users = result.fetchone()[0]
            print(f"   S3: {s3_users:,} registros")
            print(f"   DIFERENCIA: {oracle_users - s3_users:,} registros")
            
        except Exception as e:
            print(f"   S3 Error: {e}")
        
        # 4. RESUMEN
        print("\n" + "=" * 70)
        print("RESUMEN DE DIFERENCIAS:")
        print("=" * 70)
        
        total_oracle = oracle_mod + oracle_auth + oracle_users
        print(f"Total registros Oracle origen: {total_oracle:,}")
        print(f"  - USER_MODIFICATION_HISTORY: {oracle_mod:,}")
        print(f"  - USER_AUTH_CHANGE_HISTORY: {oracle_auth:,}")
        print(f"  - USER_PROFILE: {oracle_users:,}")
        
        try:
            total_s3 = s3_mod + s3_auth + s3_users
            print(f"Total registros S3 origen: {total_s3:,}")
            print(f"  - USER_MODIFICATION_HISTORY: {s3_mod:,}")
            print(f"  - USER_AUTH_CHANGE_HISTORY: {s3_auth:,}")
            print(f"  - USER_PROFILE: {s3_users:,}")
            print(f"DIFERENCIA TOTAL: {total_oracle - total_s3:,} registros")
            
            if total_oracle == total_s3:
                print("✅ LOS DATOS ORIGEN SON IGUALES")
            else:
                print("🚨 HAY DIFERENCIAS EN LOS DATOS ORIGEN")
        except:
            print("❌ No se pudo calcular total S3 por errores")
        
        oracle_cursor.close()
        oracle_conn.close()
        duck_conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
