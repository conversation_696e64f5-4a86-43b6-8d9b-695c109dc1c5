#!/usr/bin/env python3
"""
Debug de cada parte del SP_LOG_USR para identificar dónde aparecen los valores None/vacíos
"""

import pandas as pd
import duckdb

def analyze_log_usr_by_requesttype():
    """Ana<PERSON>zar LOG_USR por REQUESTTYPE para identificar el origen de valores None/vacíos"""
    try:
        conn = duckdb.connect()
        
        log_usr_path = "S3_LOG_USER/output/********/LOG_USR.parquet"
        
        print("=== ANÁLISIS LOG_USR POR REQUESTTYPE ===")
        
        # Analizar TIPODOCUMENTO por REQUESTTYPE
        query = f"""
        SELECT 
            REQUESTTYPE,
            TIPODOCUMENTO,
            COUNT(*) as CANTIDAD
        FROM read_parquet('{log_usr_path}')
        GROUP BY REQUESTTYPE, TIPODOCUMENTO
        ORDER BY REQUESTTYPE, CANTIDAD DESC
        """
        
        df_by_request = conn.execute(query).df()
        print("TIPODOCUMENTO por REQUESTTYPE:")
        print(df_by_request.to_string(index=False))
        
        # Identificar específicamente los registros problemáticos
        query2 = f"""
        SELECT 
            REQUESTTYPE,
            COUNT(*) as TOTAL,
            COUNT(CASE WHEN TIPODOCUMENTO IS NULL THEN 1 END) as NULL_COUNT,
            COUNT(CASE WHEN TIPODOCUMENTO = '' THEN 1 END) as EMPTY_COUNT,
            COUNT(CASE WHEN TIPODOCUMENTO = 'None' THEN 1 END) as NONE_COUNT
        FROM read_parquet('{log_usr_path}')
        GROUP BY REQUESTTYPE
        ORDER BY NULL_COUNT + EMPTY_COUNT + NONE_COUNT DESC
        """
        
        df_problems = conn.execute(query2).df()
        print(f"\nRegistros problemáticos por REQUESTTYPE:")
        print(df_problems.to_string(index=False))
        
        # Ejemplos de registros problemáticos
        query3 = f"""
        SELECT 
            USERHISTID,
            REQUESTTYPE,
            TIPODOCUMENTO,
            DOCUMENTO,
            BANKDOMAIN
        FROM read_parquet('{log_usr_path}')
        WHERE TIPODOCUMENTO IS NULL OR TIPODOCUMENTO = '' OR TIPODOCUMENTO = 'None'
        LIMIT 15
        """
        
        df_examples = conn.execute(query3).df()
        print(f"\nEjemplos de registros problemáticos:")
        print(df_examples.to_string(index=False))
        
        return df_by_request
        
    except Exception as e:
        print(f"Error analizando LOG_USR: {e}")
        return None

def analyze_user_modification_join():
    """Analizar específicamente el JOIN de USER_MODIFICATION"""
    try:
        conn = duckdb.connect()
        
        user_data_path = "S3_LOG_USER/TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet"
        user_mod_path = "S3_LOG_USER/TEMP_LOGS_USUARIOS/********/USER_MODIFICATION_DAY.parquet"
        
        print("\n=== ANÁLISIS USER_MODIFICATION JOIN ===")
        
        # Verificar el JOIN entre USER_MODIFICATION y USER_DATA_TRX
        query = f"""
        SELECT 
            COUNT(*) as TOTAL_MODIFICATIONS,
            COUNT(ud.O_USER_ID) as MATCHED_USERS,
            COUNT(CASE WHEN ud.ID_TYPE IS NOT NULL THEN 1 END) as WITH_ID_TYPE,
            COUNT(CASE WHEN ud.ID_TYPE IS NULL THEN 1 END) as NULL_ID_TYPE
        FROM read_parquet('{user_mod_path}') umh
        LEFT JOIN read_parquet('{user_data_path}') ud ON umh.user_id = ud.O_USER_ID
        """
        
        df_mod_join = conn.execute(query).df()
        print("Estadísticas JOIN USER_MODIFICATION:")
        print(df_mod_join.to_string(index=False))
        
        # Ejemplos de modificaciones sin match
        query2 = f"""
        SELECT 
            umh.user_id,
            umh.REQUEST_TYPE,
            ud.O_USER_ID,
            ud.ID_TYPE
        FROM read_parquet('{user_mod_path}') umh
        LEFT JOIN read_parquet('{user_data_path}') ud ON umh.user_id = ud.O_USER_ID
        WHERE ud.O_USER_ID IS NULL
        LIMIT 10
        """
        
        df_no_match = conn.execute(query2).df()
        print(f"\nEjemplos de USER_MODIFICATION sin match en USER_DATA_TRX:")
        print(df_no_match.to_string(index=False))
        
        return df_mod_join
        
    except Exception as e:
        print(f"Error analizando USER_MODIFICATION JOIN: {e}")
        return None

def analyze_auth_change_join():
    """Analizar específicamente el JOIN de AUTH_CHANGE"""
    try:
        conn = duckdb.connect()
        
        session = boto3.Session()
        conn.execute("INSTALL httpfs;")
        conn.execute("LOAD httpfs;")
        
        credentials = session.get_credentials()
        conn.execute(f"""
        SET s3_region='us-east-1';
        SET s3_access_key_id='{credentials.access_key}';
        SET s3_secret_access_key='{credentials.secret_key}';
        """)
        
        if credentials.token:
            conn.execute(f"SET s3_session_token='{credentials.token}';")
        
        user_data_path = "S3_LOG_USER/TEMP_LOGS_USUARIOS/********/USER_DATA_TRX.parquet"
        auth_change_path = "S3_LOG_USER/TEMP_LOGS_USUARIOS/********/USER_AUTH_CHANGE_HISTORY.parquet"
        user_identifier_path = "s3://prd-datalake-silver-zone-637423440311/PDP_PROD10_MAINDB/USER_IDENTIFIER_ORA/consolidado_puro.parquet"
        
        print("\n=== ANÁLISIS AUTH_CHANGE JOIN ===")
        
        # Verificar el JOIN completo AUTH_CHANGE -> USER_IDENTIFIER -> USER_DATA_TRX
        query = f"""
        SELECT 
            COUNT(*) as TOTAL_AUTH_CHANGES,
            COUNT(ui.USER_ID) as MATCHED_IDENTIFIERS,
            COUNT(ud.O_USER_ID) as MATCHED_USERS,
            COUNT(CASE WHEN ud.ID_TYPE IS NOT NULL THEN 1 END) as WITH_ID_TYPE,
            COUNT(CASE WHEN ud.ID_TYPE IS NULL THEN 1 END) as NULL_ID_TYPE
        FROM read_parquet('{auth_change_path}') uach
        INNER JOIN read_parquet('{user_identifier_path}') ui ON uach.AUTHENTICATION_ID = ui.AUTHENTICATION_ID
        LEFT JOIN read_parquet('{user_data_path}') ud ON ui.USER_ID = ud.O_USER_ID
        """
        
        df_auth_join = conn.execute(query).df()
        print("Estadísticas JOIN AUTH_CHANGE:")
        print(df_auth_join.to_string(index=False))
        
        # Ejemplos de AUTH_CHANGE sin match final
        query2 = f"""
        SELECT 
            uach.AUTHENTICATION_ID,
            ui.USER_ID,
            ud.O_USER_ID,
            ud.ID_TYPE
        FROM read_parquet('{auth_change_path}') uach
        INNER JOIN read_parquet('{user_identifier_path}') ui ON uach.AUTHENTICATION_ID = ui.AUTHENTICATION_ID
        LEFT JOIN read_parquet('{user_data_path}') ud ON ui.USER_ID = ud.O_USER_ID
        WHERE ud.O_USER_ID IS NULL
        LIMIT 10
        """
        
        df_auth_no_match = conn.execute(query2).df()
        print(f"\nEjemplos de AUTH_CHANGE sin match final:")
        print(df_auth_no_match.to_string(index=False))
        
        return df_auth_join
        
    except Exception as e:
        print(f"Error analizando AUTH_CHANGE JOIN: {e}")
        return None

def main():
    """Función principal"""
    print("Debug de partes del SP_LOG_USR")
    print("="*60)
    
    # Analizar por REQUESTTYPE
    analyze_log_usr_by_requesttype()
    
    # Analizar JOIN de USER_MODIFICATION
    analyze_user_modification_join()
    
    # Analizar JOIN de AUTH_CHANGE
    import boto3
    analyze_auth_change_join()
    
    print("\n" + "="*60)
    print("CONCLUSIÓN:")
    print("Identificar qué parte del SP_LOG_USR está generando valores None/vacíos")

if __name__ == "__main__":
    main()
