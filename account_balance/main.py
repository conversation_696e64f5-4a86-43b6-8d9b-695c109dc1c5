# -*- coding: utf-8 -*-
"""
Created on Fri Feb 28 08:57:59 2025

@author: hfernandez
"""

import logging
from logging.handlers import TimedRotatingFileHandler
import boto3
import pandas as pd
import os
import sys
from datetime import datetime, timedelta
from config import bucket_s3,input_path, private_key_path, public_key_path
from file_signer.file_signer import FileSigner

# Obtener la fecha de ayer en formato 'YYYYMMDD'
full_datetime = (datetime.now() - timedelta(days=0))
full_datetime_old = (datetime.now() - timedelta(days=1))
process_time = full_datetime.strftime('%H%M%S')

input_date = sys.argv[1]
data_day = datetime.strptime(input_date, '%Y/%m/%d') + timedelta(days=1)
file_day = datetime.strptime(input_date, '%Y/%m/%d') + timedelta(days=1)
path_day = data_day.strftime('%Y-%m-%d')
data_day = data_day.strftime('%Y%m%d')
#data_day = full_datetime.strftime('%Y%m%d')
#data_day_old = full_datetime.strftime('%Y%m%d')
file_data_day = file_day.strftime('%Y%m%d')

# Configurar el logger
log_dir = 'logs'
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_filename = os.path.join(log_dir, 'output.log')
handler = TimedRotatingFileHandler(log_filename, when="midnight", interval=1)
handler.suffix = "%Y%m%d"
logging.basicConfig(level=logging.INFO, handlers=[handler])

def upload_s3(file_route, file_name, s3_key):
    s3_client = boto3.client('s3')
    s3_client.upload_file(file_route, bucket_s3, f"{s3_key}{file_name}")
    logging.info("Archivo: %s subido a: s3://%s", file_name, f"{bucket_s3}/{s3_key}{file_name}")

def dividir_archivo_por_bancodominio(archivo_entrada):
    # Leer el archivo CSV original
    df = pd.read_csv(archivo_entrada, dtype=str)

    # Obtener el sufijo (POST o PREV) del nombre del archivo
    nombre_archivo = os.path.basename(archivo_entrada)
    sufijo = nombre_archivo.split('-')[-1].split('.')[0]

    # Crear las carpetas POST y PREV si no existen
    carpeta_salida = sufijo.upper()  # POST o PREV
    if not os.path.exists(carpeta_salida):
        os.makedirs(carpeta_salida)
    
    # Iterar sobre los valores únicos en la columna 'bankDomain'
    for bank_domain in df['bankDomain'].unique():
        # Filtrar el DataFrame por el valor de 'bankDomain'
        df_filtrado = df[df['bankDomain'] == bank_domain]
        if bank_domain == 'FCONFIANZA':
            bank_domain = '0231FCONFIANZA'
        # Generar el nombre del archivo de salida con la ruta de la carpeta correspondiente
        archivo_salida = os.path.join(carpeta_salida, f"USRACCOUNTBALANCE-{bank_domain}-{file_data_day}{process_time}.csv")
        
        # Guardar el DataFrame filtrado en un nuevo archivo CSV
        df_filtrado.to_csv(archivo_salida, index=False)
        print(f"Archivo guardado: {archivo_salida}")
        # Subir el archivo a S3
        s3_key = f"{bank_domain}/{path_day}/USER-BALANCES/"
        upload_s3(archivo_salida, os.path.basename(archivo_salida), s3_key)
        
        #Firmado de archivos:
        signer = FileSigner(private_key_path)
        signer.sign_file(archivo_salida)
        signature_path = f"{archivo_salida}.signature"
        is_valid = signer.verify_signature(archivo_salida, signature_path, public_key_path)
        signature_file = os.path.basename(archivo_salida) + ".signature"
        if is_valid:
            upload_s3(signature_path, signature_file, s3_key)

# Llamar a la función con el nombre del archivo que quieras procesar
#dividir_archivo_por_bancodominio(f"{input_path}/user-account-balances-{data_day_old}.csv")
dividir_archivo_por_bancodominio(f"{input_path}/user-account-balances-{data_day}_000000.csv")
