#!/bin/bash

if [ -z "$1" ]; then
    fecha=$(date -d "yesterday" +"%Y/%m/%d")
else
    fecha=$1
fi

#fecha_path=$(date -d "$fecha + 1 day" +"%Y%m%d")
LOG_FILE="execution_status.log"

echo "$fecha"

cd /home/<USER>/generate/

# Crear carpetas de logs
mkdir -p logs/{excel,csv,log_transacciones,reports32a_b,account_balances,log_usuarios,prepare,reporte_conciliacion,csv_to_pdf,prepare_rds,mysql_reports}

echo "== Instalando dependencias si faltan =="
pip install -r requirements.txt

if [ ! -f "$LOG_FILE" ] || [ ! -s "$LOG_FILE" ]; then
	echo "$(date) - Iniciando proceso de ejecucion"
	echo "El archivo de log esta vacio. Iniciando el proceso desde cero."
fi

check_status_and_run() {
	PART_NAME=$1
	STATUS=$(grep -i "$PART_NAME" "$LOG_FILE" | awk '{print $2}')

	if [ "$STATUS" != "OK" ]; then
		echo "$(date) - Ejecutando $PART_NAME"
		python3 prepare/main.py "$fecha" "$PART_NAME"
	else
		echo "$(date) - $PART_NAME ya está marcado como OK. No se ejecuta."
	fi
}

check_status_and_run "POBLAR-NULL"
check_status_and_run "USER-MODIFY"
check_status_and_run "BALANCE"
check_status_and_run "LOG-TRX"
check_status_and_run "LOG-USR"

if ! grep -q "PROCESO COMPLETADO" "$LOG_FILE"; then
	echo "$fecha" "- PROCESO COMPLETADO" >> "$LOG_FILE"
fi

echo "Proceso finalizado. Revisa el archivo de log: $LOG_FILE"

