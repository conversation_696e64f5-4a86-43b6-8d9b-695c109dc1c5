#!/bin/bash

# Variables de entorno
export PRIVATE_KEY_PATH="/home/<USER>/generate/FileSigner/private_key.key"
#export PUBLIC_KEY_PATH="/home/<USER>/generate/FileSigner/public_key.pem"
export PRIVATE_CRT_PATH="/home/<USER>/generate/FileSigner/private_key.crt"
export OUTPUT_ROUTE="/home/<USER>/output/excel/"
export OUTPUT_ROUTE_CSV="/home/<USER>/output/csv/"

# Definir el array con los valores
values32=("32B-I" "32B-II" "32B-III" "32B-IV" "32B-V")
values=("COMISIONES-BIMER" "SERVICE-PROVIDER" "CASHIN" "CASHOUT" "AGENTES-BIM" "SERVICIOS-DIRECTOS" "COMERCIOS" "RECARGAS" "TRAZA-FEE" "RETIROS" "DEPOSITOS")
#valuescsv=("MTX-TRANSACTION" "LOG-TRANSACCIONES")
valuescsv=("AZULITO" "INTEROPE-COBRAR-PDF" "INTEROPE-PAGAR-PDF" "LOG-USUARIOS" "UNIQUE" "MOVISTAR" "ENTEL" "RETIRO-SENTINEL" "RETIRO-WU-HUB" "USER-BALANCES" "QR-NIUBIZ" "QR-IZIPAY" "INTEROPE-COBRAR" "INTEROPE-PAGAR" "MTX-TRANSACTION" "LOG-TRANSACCIONES" "32A" "32B-I" "32B-II" "32B-III" "32B-IV" "32B-V" "COMISIONES")
export REPORTS_NO_S3="LOG-USUARIOS,LOG-TRANSACCIONES,MTX-TRANSACTION,USER-BALANCES,32A,32B-I,32B-II,32B-III,32B-IV,32B-V,INTEROPE-COBRAR-PDF,INTEROPE-PAGAR-PDF"

ROUTE_CSV="/home/<USER>/output/csv"
TARGET_PATH="/home/<USER>/output/load_rds"

if [ -z "$1" ]; then
    fecha=$(date -d "yesterday" +"%Y/%m/%d")
else
    fecha=$1
fi

fecha_path=$(date -d "$fecha + 1 day" +"%Y%m%d")


#Nos posicionamos en la raiz
cd /home/<USER>/generate/

# Crear carpeta de logs si no existe
mkdir -p logs
mkdir -p logs/excel
mkdir -p logs/csv
mkdir -p logs/log_transacciones
mkdir -p logs/reports32a_b
mkdir -p logs/account_balances
mkdir -p logs/log_usuarios
mkdir -p logs/prepare
mkdir -p logs/reporte_conciliacion
mkdir -p logs/prepare_rds
mkdir -p logs//mysql_reports

pip install -r requirements.txt

# Preparando las tablas para generacion de reportes
python3 prepare/main.py "$fecha" > "logs/prepare/PRE-REPORTES.log" 2>&1 &
wait

# Ejecutar en paralelo
for value in "${values[@]}"; do
    python3 exports_excel/main.py "$value" "$fecha"> "logs/excel/${value}.log" 2>&1 &
done

for value in "${valuescsv[@]}"; do
    python3 exports_csv/main.py "$value" "$fecha"> "logs/csv/${value}.log" 2>&1 &
done
wait

cd /home/<USER>/generate/log_transacciones/
mkdir output/"$fecha_path"
python3 procesar.py "$fecha" > "/home/<USER>/generate/logs/log_transacciones/LOG-TRANSACCIONES.log" 2>&1 &
wait

cd /home/<USER>/generate/log_usuarios/
python3 procesar.py "$fecha" > "/home/<USER>/generate/logs/log_usuarios/LOG-USUARIOS.log" 2>&1 &
wait

cd /home/<USER>/generate/account_balance/
python3 main.py "$fecha" > "/home/<USER>/generate/logs/account_balances/ACC-BALANCES.log" 2>&1 &
wait

cd /home/<USER>/generate/reports32a-b/
for value in "${values32[@]}"; do
    python3 main.py "$value" "$fecha" > "/home/<USER>/generate/logs/reports32a_b/${value}.log" 2>&1 &
done
wait

cd /home/<USER>/generate/mysql_reports/GOPAY
python3 main.py "$fecha" > "/home/<USER>/generate/logs/mysql_reports/GOPAY.log" 2>&1 &
wait

cd /home/<USER>/generate/mysql_reports/Fullcargas
python3 main.py "$fecha" > "/home/<USER>/generate/logs/mysql_reports/FULLCARGAS.log" 2>&1 &
wait 

cd /home/<USER>/generate/mysql_reports/Servicios-WU
python3 main.py "$fecha" > "/home/<USER>/generate/logs/mysql_reports/SERVICIOS-WU.log" 2>&1 &
wait

cd /home/<USER>/generate/reporte_conciliacion/
python3 main.py "$fecha" > "/home/<USER>/generate/logs/reporte_conciliacion/CONCILIACION-BIM.log" 2>&1 &
wait


log_trx_file="TR-${fecha_path}.csv"
mtx_trx_header_file="MTX_TRANSACTION_HEADER_${fecha_path}.csv"

#Creamos la ruta para mover los archivos que se cargaran al RDS
mkdir -p $TARGET_PATH

# Archivos de destino renombrados
log_trx_new_name="LOG_TRX_FINAL.csv"
mtx_trx_new_name="MTX_TRANSACTION_HEADER.csv"


# Verificar si el archivo TR-YYYYMMDD.csv existe y moverlo
if [ -f "$ROUTE_CSV/$log_trx_file" ]; then
    mv "$ROUTE_CSV/$log_trx_file" "$TARGET_PATH/$log_trx_new_name"
    echo "Archivo $log_trx_file movido a $log_trx_new_name"
else
    echo "No se encontro el archivo $log_trx_file"
fi

# Verificar si el archivo MTX_TRANSACTION_HEADER_YYYYMMDD.csv existe y moverlo
if [ -f "$ROUTE_CSV/$mtx_trx_header_file" ]; then
    mv "$ROUTE_CSV/$mtx_trx_header_file" "$TARGET_PATH/$mtx_trx_new_name"
    echo "Archivo $mtx_trx_header_file movido a $mtx_trx_new_name"
else
    echo "No se encontro el archivo $mtx_trx_header_file"
fi

cd /home/<USER>/generate/prepare_rds/
python3 read_csv_sql.py "$TARGET_PATH" > "/home/<USER>/generate/logs/prepare_rds/CARGAR-RDS.log" 2>&1 &
wait
