#!/usr/bin/env python3
"""
Análisis DISTINCT TIPODOCUMENTO: Oracle vs Nuestro Pipeline
Data Engineering: Comparación exhaustiva
"""

import oracledb
import duckdb
import boto3
import pandas as pd

def analizar_oracle():
    """<PERSON><PERSON><PERSON> DISTINCT TIPODOCUMENTO en Oracle"""
    try:
        print("📊 ANALIZANDO ORACLE LOG_USR...")
        
        conn = oracledb.connect(
            user='usr_datalake',
            password='U2024b1mD4t4l4k5',
            dsn='*************:1521/MMONEY'
        )
        cursor = conn.cursor()
        
        # DISTINCT TIPODOCUMENTO
        cursor.execute("""
            SELECT 
                TIPODOCUMENTO,
                COUNT(*) as cantidad
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TO_DATE('2025-06-03', 'YYYY-MM-DD')
            GROUP BY TIPODOCUMENTO
            ORDER BY COUNT(*) DESC
        """)
        
        oracle_results = cursor.fetchall()
        oracle_dict = {}
        oracle_total = 0
        
        print("\n🔍 ORACLE TIPODOCUMENTO DISTINCT:")
        print("=" * 50)
        
        for row in oracle_results:
            tipodoc = row[0] if row[0] is not None else "NULL"
            cantidad = row[1]
            oracle_dict[row[0]] = cantidad
            oracle_total += cantidad
            
            print(f"   '{tipodoc}': {cantidad:,} registros")
        
        print(f"\n📈 Total Oracle: {oracle_total:,} registros")
        
        cursor.close()
        conn.close()
        
        return oracle_dict, oracle_total
        
    except Exception as e:
        print(f"❌ Error Oracle: {e}")
        return {}, 0

def analizar_s3():
    """Analizar DISTINCT TIPODOCUMENTO en nuestro pipeline S3"""
    try:
        print("\n📊 ANALIZANDO NUESTRO PIPELINE S3...")
        
        # Configurar DuckDB
        session = boto3.Session()
        credentials = session.get_credentials().get_frozen_credentials()
        
        conn = duckdb.connect()
        conn.sql('INSTALL httpfs;')
        conn.sql('LOAD httpfs;')
        conn.sql('SET s3_region=\'us-east-1\';')
        conn.sql('SET s3_use_ssl=true;')
        conn.sql('SET s3_url_style=\'path\';')
        conn.sql(f'SET s3_access_key_id=\'{credentials.access_key}\';')
        conn.sql(f'SET s3_secret_access_key=\'{credentials.secret_key}\';')
        if credentials.token:
            conn.sql(f'SET s3_session_token=\'{credentials.token}\';')
        
        # DISTINCT TIPODOCUMENTO
        result = conn.sql("""
            SELECT 
                TIPODOCUMENTO,
                COUNT(*) as cantidad
            FROM read_parquet("output/20250603/LOG_USR.parquet")
            GROUP BY TIPODOCUMENTO
            ORDER BY COUNT(*) DESC
        """)
        
        s3_results = result.fetchall()
        s3_dict = {}
        s3_total = 0
        
        print("\n🔍 S3 TIPODOCUMENTO DISTINCT:")
        print("=" * 50)
        
        for row in s3_results:
            tipodoc = row[0] if row[0] is not None else "NULL"
            cantidad = row[1]
            s3_dict[row[0]] = cantidad
            s3_total += cantidad
            
            print(f"   '{tipodoc}': {cantidad:,} registros")
        
        print(f"\n📈 Total S3: {s3_total:,} registros")
        
        conn.close()
        
        return s3_dict, s3_total
        
    except Exception as e:
        print(f"❌ Error S3: {e}")
        return {}, 0

def comparar_resultados(oracle_dict, oracle_total, s3_dict, s3_total):
    """Comparar resultados Oracle vs S3"""
    
    print("\n" + "=" * 80)
    print("🔍 COMPARACIÓN ORACLE vs S3")
    print("=" * 80)
    
    # Obtener todos los valores únicos
    all_values = set(oracle_dict.keys()) | set(s3_dict.keys())
    
    print("\nVALOR TIPODOCUMENTO | ORACLE    | S3        | DIFERENCIA | STATUS")
    print("-" * 70)
    
    issues = []
    perfect_match = True
    
    for value in sorted(all_values, key=lambda x: oracle_dict.get(x, 0), reverse=True):
        oracle_count = oracle_dict.get(value, 0)
        s3_count = s3_dict.get(value, 0)
        diff = s3_count - oracle_count
        
        # Determinar status
        if diff == 0:
            status = "✅ PERFECTO"
        elif oracle_count == 0:
            status = "⚠️ EXTRA EN S3"
            perfect_match = False
            issues.append(f"'{value}' existe en S3 ({s3_count:,}) pero NO en Oracle")
        elif s3_count == 0:
            status = "❌ FALTA EN S3"
            perfect_match = False
            issues.append(f"'{value}' existe en Oracle ({oracle_count:,}) pero NO en S3")
        else:
            status = "⚠️ DIFERENTE"
            perfect_match = False
            issues.append(f"'{value}': Oracle {oracle_count:,} vs S3 {s3_count:,} (diff: {diff:+,})")
        
        display_val = "NULL" if value is None else value
        print(f"'{display_val}'".ljust(19) + f" | {oracle_count:>9,} | {s3_count:>9,} | {diff:>+9,} | {status}")
    
    # Resumen
    print("\n" + "=" * 80)
    print("📊 RESUMEN DE COMPARACIÓN")
    print("=" * 80)
    
    print(f"Total registros Oracle: {oracle_total:,}")
    print(f"Total registros S3:     {s3_total:,}")
    print(f"Diferencia total:       {s3_total - oracle_total:+,}")
    
    if perfect_match:
        print("\n🎉 ¡PERFECTO! Los valores TIPODOCUMENTO son IDÉNTICOS")
        print("✅ Distribución exacta entre Oracle y S3")
        print("✅ El pipeline está replicando correctamente")
    else:
        print(f"\n⚠️ PROBLEMAS ENCONTRADOS: {len(issues)}")
        print("\n🔍 DETALLES DE PROBLEMAS:")
        for i, issue in enumerate(issues, 1):
            print(f"   {i}. {issue}")
        
        print("\n🔧 ACCIONES RECOMENDADAS:")
        print("   1. Verificar datos en tablas S3 origen")
        print("   2. Revisar JOINs en el pipeline")
        print("   3. Validar lógica de transformación")
        print("   4. Comparar filtros de fecha")

def obtener_muestra_s3():
    """Obtener muestra de registros S3 para análisis"""
    try:
        print("\n" + "=" * 80)
        print("📋 MUESTRA DE REGISTROS S3")
        print("=" * 80)
        
        conn = duckdb.connect()
        
        result = conn.sql("""
            SELECT 
                USERHISTID,
                TIPODOCUMENTO,
                DOCUMENTO,
                REQUESTTYPE
            FROM read_parquet("output/20250603/LOG_USR.parquet")
            ORDER BY CREATEDON
            LIMIT 10
        """)
        
        muestra = result.fetchall()
        
        print("USERHISTID   | TIPODOCUMENTO | DOCUMENTO        | REQUESTTYPE")
        print("-" * 70)
        
        for row in muestra:
            userhistid = row[0] or "NULL"
            tipodoc = row[1] or "NULL"
            documento = row[2] or "NULL"
            requesttype = row[3] or "NULL"
            
            print(f"{userhistid:<12} | {tipodoc:<13} | {documento:<16} | {requesttype}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error obteniendo muestra: {e}")

def main():
    print("🔍 ANÁLISIS DISTINCT TIPODOCUMENTO - DATA ENGINEERING")
    print("Oracle LOG_USR vs Pipeline S3 - Fecha: 2025-06-03")
    print("=" * 80)
    
    # Analizar Oracle
    oracle_dict, oracle_total = analizar_oracle()
    
    # Analizar S3
    s3_dict, s3_total = analizar_s3()
    
    # Comparar resultados
    if oracle_dict or s3_dict:
        comparar_resultados(oracle_dict, oracle_total, s3_dict, s3_total)
        
        # Obtener muestra
        if s3_dict:
            obtener_muestra_s3()
    
    print("\n" + "=" * 80)
    print("✅ ANÁLISIS COMPLETADO")
    print("=" * 80)

if __name__ == "__main__":
    main()
